import {
  ref, watch, computed, ComputedRef, Ref,
  onMounted
} from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

import { showToast } from '@xhs/reds-h5-next'
import { cloneDeep } from 'lodash'
import { ValidityTimePickerValue } from './ValidityTimePicker/types'

import { getProLicense } from '~/services/edith_get_pro_license'
import { postSubjectQuery } from '~/services/edith_post_subject_query'
import { getQueryEffectiveQualification } from '~/services/edith_get_query_effective_qualification'
import { getPreCheck } from '~/services/edith_get_pre_check'
import { ClaimTypeEnum } from '~/enum/shop'
import { postPoiGetbizcompanyinfo } from '~/services/shop'

// 业务执照信息类型定义
export interface BusinessLicenseInfo {
  companyName?: string
  uniformCreditCode?: string
  businessAddress?: string
  authorizationName?: string // 法定代表人信息
  businessLicense?: {
    startTime?: number
    endTime?: number
    permanent?: boolean
    qualificationCode?: string
    qualificationName?: string
    indexId?: string
  }
  fileAttachmentList?: {
    fileName?: string
    fileType?: string
    height?: number
    width?: number
    uploaderInfoModel: {
      fileId?: string
      url: string
      scene?: string
      bizName?: string
      isSecret?: boolean
      cloudType?: number
    }
  }[]
}

// 类型定义
export interface BusinessLicenseData {
  useProfessionalQualification: boolean // 是否使用专业号资质

  businessLicenseImage?: any[] // 营业执照图片信息
  businessAddress?: string // 经营地址
  uniformCreditCode?: string // 统一社会信用代码
  companyName?: string // 企业名称
  businessLicense?: string // 营业执照图片URL
  validityPeriod?: ValidityTimePickerValue | null // 营业执照有效期

  // 新增缺失的字段
  companyRegisteredAddress?: string // 公司注册地址
  registeredAddressProvince?: string // 注册地址省份
  registeredAddressCity?: string // 注册地址市
  registeredAddressStreet?: string // 注册地址详细地址
  principalType: string // 主体类型（'INDIVIDUAL_BUSINESS' | 'ENTERPRISE_PRINCIPAL'）
  authorizationName?: string // 法定代表人名称
  qualificationCode?: string // 资质唯一标识码（从OCR获取）
  qualificationName?: string // 资质名称
  indexId?: string // 用于前端定位的索引ID

  [key: string]: any
}

// 专业号类型枚举 - 用于判断是否为大陆个体或大陆企业
export enum ProfessionalQualificationType {
  MAINLAND_INDIVIDUAL = 1, // 大陆个体
  MAINLAND_ENTERPRISE = 2, // 大陆企业
  OVERSEAS_INDIVIDUAL = 3, // 海外个体
  OVERSEAS_ENTERPRISE = 4, // 海外企业
}

// 通用表单返回类型
export interface BaseBusinessLicenseFormReturn {
  formData: Ref<BusinessLicenseData>
  formRules: ComputedRef<Record<string, any>>
  isEdit: ComputedRef<boolean>
  isPreview: ComputedRef<boolean>
  isLicenseInfoExtracted: Ref<boolean>

  // 新增loading状态
  isInitializing: Ref<boolean> // 页面初始化loading
  isProfessionalQualificationLoading: Ref<boolean> // 专业号资质loading
  // poiId: ComputedRef<string>

  // 营业类型相关
  businessTypeSheetVisible: Ref<boolean>
  businessTypeText: ComputedRef<string>
  businessTypePickerColumns: ComputedRef<any[]>
  businessTypePickerValue: ComputedRef<string[]>
  subjectType: Ref<string | undefined> // 新增：主体类型

  // 草稿状态检测
  showDraftAlert: Ref<boolean> // 是否显示草稿弹窗

  // 通用方法
  formatValidityPeriod: (validityPeriod?: ValidityTimePickerValue | null) => string
  showBusinessTypeSheet: () => void
  hideBusinessTypeSheet: () => void
  confirmBusinessTypePicker: (value: string[]) => void
  handleLicenseInfoExtracted: (info: any) => void
  handleDraftAlert: (confirmed: boolean) => void // 处理草稿弹窗

  // 专业号资质相关
  getAndSetEffectiveQualification: () => Promise<boolean>
  isProfessionalQualificationEditable: ComputedRef<boolean>
}

// 通用配置选项
interface BusinessLicenseFormOptions {
  initialData?: Partial<BusinessLicenseData>
  props?: Record<string, any>
}

// 营业类型选项
const BUSINESS_TYPE_OPTIONS = [
  { value: 'INDIVIDUAL_BUSINESS', label: '个体工商户营业执照' },
  { value: 'ENTERPRISE_PRINCIPAL', label: '企业营业执照' }
]

// 根据subjectType获取可选的营业类型选项
const getAvailableBusinessTypeOptions = (subjectType?: string) => {
  if (subjectType === 'BUSINESS_PERSON') {
    // 个体工商户
    return BUSINESS_TYPE_OPTIONS.filter(option => option.value === 'INDIVIDUAL_BUSINESS')
  }
  if (subjectType === 'COMPANY') {
    // 企业
    return BUSINESS_TYPE_OPTIONS.filter(option => option.value === 'ENTERPRISE_PRINCIPAL')
  }
  // 默认返回所有选项
  return BUSINESS_TYPE_OPTIONS
}

// 根据 principalType 计算 businessLicenseType（用于需要数字类型的场景）
export const getBusinessLicenseTypeFromPrincipalType = (principalType: string): number => {
  if (principalType === 'INDIVIDUAL_BUSINESS') return 0 // 个体工商户
  if (principalType === 'ENTERPRISE_PRINCIPAL') return 1 // 企业
  return 0 // 默认为个体工商户
}

// 根据 businessLicenseType 反向计算 principalType
const getPrincipalTypeFromBusinessLicenseType = (businessLicenseType?: number): string => {
  if (businessLicenseType === 0) return 'INDIVIDUAL_BUSINESS' // 个体工商户
  if (businessLicenseType === 1) return 'ENTERPRISE_PRINCIPAL' // 企业
  return 'INDIVIDUAL_BUSINESS' // 默认为个体工商户
}

// 创建默认表单数据的工厂函数
function createDefaultFormData(initialData?: Partial<BusinessLicenseData>): BusinessLicenseData {
  const defaultPrincipalType = 'INDIVIDUAL_BUSINESS' // 默认值为"个体工商户营业执照"
  return {
    useProfessionalQualification: true, // 默认值为"是"
    principalType: defaultPrincipalType,
    businessLicense: '', // 营业执照图片URL
    validityPeriod: null, // 营业执照有效期
    businessAddress: '', // 经营地址
    uniformCreditCode: '', // 统一社会信用代码
    companyName: '', // 企业名称
    businessLicenseImage: [], // 营业执照图片信息

    // 新增字段的默认值
    companyRegisteredAddress: '', // 公司注册地址
    registeredAddressProvince: '', // 注册地址省份
    registeredAddressCity: '', // 注册地址市
    registeredAddressStreet: '', // 注册地址详细地址
    authorizationName: '', // 法定代表人名称
    qualificationCode: '', // 资质唯一标识码
    qualificationName: '', // 资质名称
    indexId: '', // 用于前端定位的索引ID

    ...initialData
  }
}

// 提取的通用方法：从store获取并设置营业执照数据
function setBusinessLicenseDataFromStore(
  store: any,
  formData: Ref<BusinessLicenseData>,
  isLicenseInfoExtracted: Ref<boolean>
): boolean {
  const storeData = store.state.ClaimShopStore?.claimStorePageFormData
  if (!storeData) return false

  const {
    companyName,
    businessAddress,
    usci,
    businessLicenseValidity,
    businessLicenseType,
    businessLicenseImage,
    useProCertification,
  } = storeData
  // 优先使用store中的数据（用户可能已经修改过）
  const hasStoreData = companyName
    || businessAddress
    || usci
    || businessLicenseImage?.url
    || businessLicenseValidity

  if (hasStoreData) {
    // 使用store中已有的数据
    Object.assign(formData.value, {
      companyName: companyName || '',
      businessAddress: businessAddress || '',
      uniformCreditCode: usci || '',
      validityPeriod: businessLicenseValidity ? {
        qualValidityPeriod: businessLicenseValidity.qualValidityPeriod,
        startTime: businessLicenseValidity.startTime || 0,
        endTime: businessLicenseValidity.endTime || 0
      } : null,
      principalType: getPrincipalTypeFromBusinessLicenseType(businessLicenseType),
      businessLicense: businessLicenseImage?.url || '',
      businessLicenseImage: businessLicenseImage ? [{
        uploaderInfoModel: {
          url: businessLicenseImage.url
        },
        width: businessLicenseImage.width,
        height: businessLicenseImage.height
      }] : [],
      useProfessionalQualification: useProCertification
    })
    isLicenseInfoExtracted.value = true
    return true
  }

  return false
}

export function useBusinessLicenseForm(options: BusinessLicenseFormOptions = {}): BaseBusinessLicenseFormReturn {
  const route = useRoute()
  const store = useStore()
  const userInfoStore = store.state.ShopUserInfoStore
  // 判断是否为轻认领
  const isLightClaim = computed(() => userInfoStore.customClaimType === ClaimTypeEnum.LIGHT_CLAIM)
  const isSettle = computed(() => options.props?.isSettle)

  const shopApplyState = computed(() =>
    store.state.ShopApplyStore.shopApplyState)

  // 新增：主体类型状态，通过getPreCheck接口获取
  const subjectType = ref<string | undefined>(undefined)

  // 状态判断
  const isEdit = computed(() => {
    // 入驻场景：通过shopApplyState.id判断
    if (isSettle.value) {
      return !!shopApplyState.value.id
    }
    // 认领场景：基于是否有shopId或id参数判断
    const claimStoreData = store.state.ClaimShopStore?.claimStorePageFormData
    const { id, shopId } = claimStoreData || {}
    return !!(id || shopId)
  })
  const isPreview = computed(() => Boolean(route.query.isPreview))

  const formData = ref<BusinessLicenseData>(createDefaultFormData(options.initialData))
  const isLicenseInfoExtracted = ref(false)
  const businessTypeSheetVisible = ref(false)

  // 添加loading状态
  const isInitializing = ref(false) // 页面初始化loading
  const isProfessionalQualificationLoading = ref(false) // 专业号资质loading

  // 初始化完成标志位，用于避免初始化期间watch清空数据
  const isInitializationComplete = ref(false)

  // 草稿状态检测
  const showDraftAlert = ref(false) // 是否显示草稿弹窗

  const formRules = computed(() => ({
    businessLicense: [{
      type: 'string',
      required: true,
      validator: (_rule: any, val: string) => val?.trim() !== '',
      message: '请上传营业执照'
    }],
    uniformCreditCode: [{
      type: 'string',
      required: true,
      validator: (_rule: any, val: string) => {
        if (isLightClaim.value) {
          return true
        }
        return val?.trim() !== ''
      },
      message: '请输入统一社会信用代码'
    }],
    companyName: [{
      type: 'string',
      required: true,
      validator: (_rule: any, val: string) => {
        if (isLightClaim.value) {
          return true
        }
        return val?.trim() !== ''
      },
      message: '请输入企业名称'
    }],
    businessAddress: [{
      type: 'string',
      required: true,
      validator: (_rule: any, val: string) => {
        if (isLightClaim.value) {
          return true
        }
        return val?.trim() !== ''
      },
      message: '请输入经营地址'
    }],
    validityPeriod: [{
      type: 'object',
      required: true,
      validator: (_rule: any, val: ValidityTimePickerValue|null) => {
        if (isLightClaim.value) {
          return true
        }
        if (!val || typeof val !== 'object') return false
        if (val?.qualValidityPeriod === 0) return true // 永久有效
        return !!(val?.startTime && val?.endTime)
      },
      message: '请选择营业执照有效期'
    }],
  }))

  // 查询营业执照信息
  const init = async () => {
    isInitializing.value = true // 开始loading
    try {
      // 只有入驻状态时才需要调用getPreCheck获取subjectType限制,并且查询入驻进度，展示草稿态
      if (isSettle.value) {
        try {
          const preCheckData = await getPreCheck()
          if (preCheckData?.proUserInfo?.subjectType) {
            subjectType.value = preCheckData.proUserInfo.subjectType

            // 根据subjectType获取可选项并设置默认值
            const availableOptions = getAvailableBusinessTypeOptions(preCheckData.proUserInfo.subjectType)
            if (availableOptions.length === 1) {
              // 如果只有一个选项，自动选中该选项
              formData.value.principalType = availableOptions[0].value
            }
          }
        } catch (error) {
          console.warn('获取用户基本信息失败:', error)
        }
      }

      // 非预览状态，且非编辑状态 - 优先处理新建状态
      if (!isPreview.value && !isEdit.value) {
        // 本地认领新建状态
        if (!isLightClaim.value && !isSettle.value) {
          const hasStoreData = setBusinessLicenseDataFromStore(store, formData, isLicenseInfoExtracted)
          // 如果store中没有数据，则是新建且无草稿，则需要获取专业号资质信息
          if (!hasStoreData) {
            const success = await getAndSetEffectiveQualification()
            if (success) {
            // 如果获取成功，将专业号资质开关设为true
              formData.value.useProfessionalQualification = true
            } else {
            // 如果获取失败，保持为false
              formData.value.useProfessionalQualification = false
            }
            return
          }
          // 如果是本地认领创建草稿态，并且使用专业号资质，则需要获取专业号资质信息
          if (hasStoreData && formData.value.useProfessionalQualification) {
            await getAndSetEffectiveQualification()
          }
          return
        }

        // 入驻新建状态
        if (isSettle.value) {
          // 优先检查store中是否有营业执照草稿数据
          const storeData = store.state.ClaimShopStore?.claimStorePageFormData
          const hasStoreDraftData = storeData && (
            storeData.companyName
            || storeData.usci
            || storeData.businessLicenseImage?.url
            || storeData.businessLicenseValidity
          )

          if (hasStoreDraftData) {
            // 如果store中有草稿数据，使用草稿数据回显
            // console.log('检测到营业执照草稿数据，进行回显')
            Object.assign(formData.value, {
              companyName: storeData.companyName || '',
              businessAddress: storeData.businessAddress || '',
              uniformCreditCode: storeData.usci || '',
              validityPeriod: storeData.businessLicenseValidity ? {
                qualValidityPeriod: storeData.businessLicenseValidity.qualValidityPeriod,
                startTime: storeData.businessLicenseValidity.startTime || 0,
                endTime: storeData.businessLicenseValidity.endTime || 0
              } : null,
              principalType: getPrincipalTypeFromBusinessLicenseType(storeData.businessLicenseType),
              businessLicense: storeData.businessLicenseImage?.url || '',
              businessLicenseImage: storeData.businessLicenseImage ? [{
                uploaderInfoModel: {
                  url: storeData.businessLicenseImage.url
                },
                width: storeData.businessLicenseImage.width,
                height: storeData.businessLicenseImage.height
              }] : [],
              useProfessionalQualification: storeData.useProfessionalQualification ?? false
            })
            isLicenseInfoExtracted.value = true

            // 如果草稿数据中使用专业号资质，则需要获取专业号资质信息进行补充
            if (storeData.useProfessionalQualification) {
              await getAndSetEffectiveQualification()
            }
          } else {
            // 如果store中没有草稿数据，按正常创建流程获取专业号资质
            const success = await getAndSetEffectiveQualification()
            if (success) {
              // 如果获取成功，将专业号资质开关设为true
              formData.value.useProfessionalQualification = true
            } else {
              // 如果获取失败，保持为false
              formData.value.useProfessionalQualification = false
            }
          }
          return
        }

        // 轻认领新建状态
        if (isLightClaim.value) {
          const success = await getAndSetEffectiveQualification()
          if (success) {
            formData.value.useProfessionalQualification = true
          } else {
            formData.value.useProfessionalQualification = false
          }
        }
        return
      }

      // 预览或编辑状态 - 加载已保存的数据

      // 入驻查询接口
      if (isSettle.value && shopApplyState.value.id) {
        const response = await postSubjectQuery({
          applyId: shopApplyState.value.id,
        })
        if (!response || !response.merchantCompanyDraft) {
          // 如果入驻查询接口没有数据，则回到初始状态
          // 由于useProfessionalQualification默认为true，需要调用专业号资质查询
          const success = await getAndSetEffectiveQualification()
          if (success) {
            formData.value.useProfessionalQualification = true
          } else {
            formData.value.useProfessionalQualification = false
          }
          return
        }
        const { merchantCompanyDraft = {} } = response
        const {
          uniformCreditCode,
          companyRegisteredAddress,
          companyName,
          businessLicense,
          principalType,
          registeredAddressProvince,
          registeredAddressCity,
          registeredAddressStreet,
          authorizationName
        } = merchantCompanyDraft

        // 填充查询到的数据
        Object.assign(formData.value, {
          useProfessionalQualification: false, // 查询入驻时，默认关闭；其他情况默认开启
          companyName: companyName || '',
          uniformCreditCode: uniformCreditCode || '',
          businessAddress: companyRegisteredAddress || '',
          companyRegisteredAddress: companyRegisteredAddress || '',
          businessLicense: businessLicense?.fileAttachmentList?.[0]?.uploaderInfoModel?.url || '',
          businessLicenseImage: businessLicense?.fileAttachmentList || [],
          validityPeriod: businessLicense ? {
            qualValidityPeriod: businessLicense.permanent ? 0 : 1, // 0表示永久有效，1表示期限有效
            startTime: businessLicense.startTime || 0,
            endTime: businessLicense.endTime || 0
          } : null,
          // 保存资质相关信息
          qualificationCode: businessLicense?.qualificationCode || '',
          qualificationName: businessLicense?.qualificationName || '',
          indexId: businessLicense?.indexId || '', // 用于前端定位的索引ID
          // 主体类型和法定代表人信息
          principalType: principalType || 'INDIVIDUAL_BUSINESS',
          authorizationName: authorizationName || '',
          // 注册地址信息
          registeredAddressProvince: registeredAddressProvince || '',
          registeredAddressCity: registeredAddressCity || '',
          registeredAddressStreet: registeredAddressStreet || ''
        })

        // 在rejectReason store中存储原始数据
        try {
          store.commit('rejectReason/SET_BUSINESS_LICENSE_ORIGINAL_DATA', cloneDeep(formData.value))
        } catch (error) {
          console.warn('存储原始表单数据到 store 失败:', error)
        }
      }

      // 轻认领 - 优先使用store数据
      if (isLightClaim.value) {
        const storeData = store.state.ClaimShopStore?.claimStorePageFormData
        if (storeData) {
          const {
            companyName,
            businessLicenseImage,
            useProCertification
          } = storeData
          console.log('storeData', !!useProCertification, storeData)
          // 直接使用store中的数据（编辑状态只能从store里拿数据）
          Object.assign(formData.value, {
            companyName: companyName || '',
            businessLicense: businessLicenseImage?.url,
            businessLicenseImage: businessLicenseImage ? [{
              uploaderInfoModel: {
                url: businessLicenseImage.url
              },
              width: businessLicenseImage.width,
              height: businessLicenseImage.height
            }] : [],
            useProfessionalQualification: !!useProCertification
          })
          isLicenseInfoExtracted.value = true
        }
      }

      // 本地认领 - 优先使用store数据，否则从接口获取
      if (!isLightClaim.value && !isSettle.value) {
        // 尝试从store获取数据
        const hasStoreData = setBusinessLicenseDataFromStore(store, formData, isLicenseInfoExtracted)

        if (!hasStoreData) {
          // 如果store中没有数据，从接口获取
          const storeData = store.state.ClaimShopStore?.claimStorePageFormData
          const { poiId, shopId } = storeData || {}

          if (poiId || shopId) {
            // 从接口获取数据
            try {
              const response = await getQueryEffectiveQualification({
                poiId,
                shopId
              })
              const effectiveQualification = response?.effectiveQualification

              if (effectiveQualification) {
                Object.assign(formData.value, {
                  companyName: effectiveQualification.companyName || '',
                  businessAddress: '', // 接口暂无此字段
                  uniformCreditCode: effectiveQualification.usci || '',
                  validityPeriod: effectiveQualification.businessLicenseValid ? {
                    qualValidityPeriod: effectiveQualification.businessLicenseValid.qualValidityPeriod || 1,
                    startTime: effectiveQualification.businessLicenseValid.startTime || 0,
                    endTime: effectiveQualification.businessLicenseValid.endTime || 0
                  } : null,
                  principalType: getPrincipalTypeFromBusinessLicenseType(effectiveQualification.businessLicenseType),
                  businessLicense: effectiveQualification.businessLicenseImage?.url || '',
                  businessLicenseImage: effectiveQualification.businessLicenseImage ? [{
                    uploaderInfoModel: {
                      url: effectiveQualification.businessLicenseImage.url
                    },
                    width: effectiveQualification.businessLicenseImage.width,
                    height: effectiveQualification.businessLicenseImage.height
                  }] : [],
                  useProfessionalQualification: false
                })
                isLicenseInfoExtracted.value = true
              }
            } catch (error) {
              console.error('本地认领获取营业执照信息失败:', error)
            }
          }
        }
      }

      // 标记数据已提取（用于预览或编辑状态）
      isLicenseInfoExtracted.value = true
    } catch (error) {
      // 静默处理错误，避免过多的错误提示
      // console.warn('查询营业执照信息失败:', error)
    } finally {
      isInitializing.value = false // 结束loading
    }
  }

  // 计算专业号相关字段是否可编辑（当专业号存在，并且开启使用专业号资质时不可编辑）
  const isProfessionalQualificationEditable = computed(() => !formData.value.useProfessionalQualification || !options.props?.canUseProfessionalQualification)

  // 营业类型相关计算属性 - 根据subjectType动态计算可选项
  const businessTypeText = computed(() => {
    const availableOptions = getAvailableBusinessTypeOptions(subjectType.value)
    const option = availableOptions.find(item => item.value === formData.value.principalType)
    return option?.label || ''
  })

  const businessTypePickerColumns = computed(() => {
    const availableOptions = getAvailableBusinessTypeOptions(subjectType.value)
    return [
      availableOptions.map(option => ({
        label: option.label,
        value: option.value
      }))
    ]
  })

  const businessTypePickerValue = computed(() => {
    // 确保默认值符合subjectType限制
    const availableOptions = getAvailableBusinessTypeOptions(subjectType.value)
    let currentValue = formData.value.principalType || 'INDIVIDUAL_BUSINESS'

    // 如果当前值不在可选范围内，使用第一个可选项作为默认值
    const isValidValue = availableOptions.some(option => option.value === currentValue)
    if (!isValidValue && availableOptions.length > 0) {
      currentValue = availableOptions[0].value
    }

    return [currentValue]
  })

  // 获取并填充专业号资质信息
  const getAndSetEffectiveQualification = async (): Promise<boolean> => {
    isProfessionalQualificationLoading.value = true // 开始loading

    try {
      // 如果是轻认领
      if (isLightClaim.value) {
        const { companyName, address, companyLicenseUrls } = await postPoiGetbizcompanyinfo()
        // 填充轻认领营业执照信息
        Object.assign(formData.value, {
          companyName: companyName || '',
          businessAddress: address || '',
          // 轻认领接口返回的是URL数组，转换为内部格式
          businessLicense: companyLicenseUrls?.[0]
        })
      } else {
        // 入驻调用专业号接口
        const res: any = await getProLicense()
        if (!res || !res.userLicense || !res.userLicense.companyName) {
          showToast('未找到有效的专业号资质')
          return false
        }

        const userLicense = res.userLicense
        // 填充本地认领营业执照信息
        Object.assign(formData.value, {
          companyName: userLicense.companyName || '',
          businessAddress: userLicense.businessAddress || '',
          companyRegisteredAddress: userLicense.companyAddress || '',
          uniformCreditCode: userLicense.licenseNo || '',
          // 如果有营业执照图片URL数组，取第一个
          businessLicense: userLicense.urls?.[0] || '',
          // 根据接口返回的有效期信息设置
          validityPeriod: {
            qualValidityPeriod: userLicense.permenant ? 0 : 1, // 0表示永久有效，1表示期限有效
            startTime: userLicense.startTime || 0,
            endTime: userLicense.endTime || 0
          },
          // 设置主体类型
          principalType: userLicense.principalType || 'INDIVIDUAL_BUSINESS'
        })
      }
      // 标记为已提取专业号資質信息
      isLicenseInfoExtracted.value = true

      return true
    } catch (error) {
      console.error('获取资质信息失败:', error)
      // 接口失败也开启专业号资质开关
      return true
    } finally {
      isProfessionalQualificationLoading.value = false // 结束loading
    }
  }

  // 显示营业类型选择器
  const showBusinessTypeSheet = () => {
    if (isPreview.value || !isProfessionalQualificationEditable.value) return
    businessTypeSheetVisible.value = true
  }

  // 隐藏营业类型选择器
  const hideBusinessTypeSheet = () => {
    businessTypeSheetVisible.value = false
  }

  // 确认营业类型选择
  const confirmBusinessTypePicker = (value: string[]) => {
    // 确保选择的值在可选范围内
    const availableOptions = getAvailableBusinessTypeOptions(subjectType.value)
    const selectedValue = value[0]
    const isValidSelection = availableOptions.some(option => option.value === selectedValue)

    if (isValidSelection) {
      formData.value.principalType = selectedValue
    }
    hideBusinessTypeSheet()
  }

  // 格式化有效期显示
  const formatValidityPeriod = (validityPeriod?: ValidityTimePickerValue | null): string => {
    if (!validityPeriod) return ''
    if (validityPeriod.qualValidityPeriod === 0) {
      return '永久有效'
    }
    if (validityPeriod.qualValidityPeriod === 1 && validityPeriod.endTime) {
      const endTime = typeof validityPeriod.endTime === 'string'
        ? validityPeriod.endTime
        : new Date(validityPeriod.endTime).toLocaleDateString()
      return `有效期至 ${endTime}`
    }
    return ''
  }

  // 处理草稿弹窗确认/取消
  const handleDraftAlert = (confirmed: boolean) => {
    showDraftAlert.value = false
    if (!confirmed) {
      // 如果取消，清空store中的id，让后续流程认为是新建状态
      store.commit('ShopApplyStore/UPDATE_SHOP_APPLY_STATE', { id: undefined })
    }
    // 如果确认，不做任何处理，保持原有的id，继续编辑流程
  }

  // 处理营业执照信息提取
  const handleLicenseInfoExtracted = (info: BusinessLicenseInfo | any) => {
    const {
      companyName,
      uniformCreditCode,
      businessAddress,
      authorizationName,
      businessLicense,
      fileAttachmentList,
      isLightClaimImageInfo
    } = info
    if (isLightClaimImageInfo) {
      formData.value.businessLicenseImage = [isLightClaimImageInfo]
    }
    // 营业执照有效期信息
    if (businessLicense) {
      formData.value.validityPeriod = {
        qualValidityPeriod: businessLicense.permanent ? 0 : 1, // 0表示永久有效，1表示期限有效
        startTime: businessLicense.startTime || 0,
        endTime: businessLicense.endTime || 0
      }

      // 正确的处理方式：保存资质相关信息到专门的字段
      if (businessLicense.qualificationCode) {
        formData.value.qualificationCode = businessLicense.qualificationCode
      }
      if (businessLicense.qualificationName) {
        formData.value.qualificationName = businessLicense.qualificationName
      }
      if (businessLicense.indexId) {
        formData.value.indexId = businessLicense.indexId
      }
    }
    if (info.checkPass) {
      isLicenseInfoExtracted.value = true
      if (info.fileAttachmentList && info.fileAttachmentList.length > 0) {
        formData.value.businessLicenseImage = info.fileAttachmentList
      }
      return
    }
    // 基础信息填充
    formData.value.companyName = companyName || ''
    formData.value.uniformCreditCode = uniformCreditCode || ''
    formData.value.businessAddress = businessAddress || ''
    formData.value.authorizationName = authorizationName || ''

    // 保存文件附件信息
    if (fileAttachmentList && fileAttachmentList.length > 0) {
      formData.value.businessLicenseImage = fileAttachmentList
    }

    // console.log('handleLicenseInfoExtracted:', formData.value)
    isLicenseInfoExtracted.value = true
  }

  // 监听专业号资质开关变化
  watch(() => formData.value.useProfessionalQualification, async newValue => {
    // 🔧 修复：在初始化期间跳过watch逻辑，避免清空接口回显的数据
    if (!isInitializationComplete.value) {
      return
    }

    if (newValue) {
      // 从专业号申请读取营业执照信息
      try {
        const success = await getAndSetEffectiveQualification()
        if (success) {
          isLicenseInfoExtracted.value = true
        } else {
          formData.value.useProfessionalQualification = false
        }
      } catch (error) {
        showToast({
          message: '读取专业号资质失败',
          duration: 2000
        })
        formData.value.useProfessionalQualification = false
      }
    } else {
      // 清空相关信息
      formData.value.businessLicense = ''
      formData.value.qualificationCode = ''
      formData.value.qualificationName = ''
      formData.value.indexId = ''
      formData.value.authorizationName = ''
      formData.value.businessLicenseImage = []
      formData.value.uniformCreditCode = ''
      formData.value.companyName = ''
      formData.value.businessAddress = ''
      formData.value.companyRegisteredAddress = ''
      formData.value.validityPeriod = null

      // 只有入驻状态才需要根据subjectType限制设置默认值
      if (isSettle.value) {
        const availableOptions = getAvailableBusinessTypeOptions(subjectType.value)
        // 使用第一个可选项作为默认值
        formData.value.principalType = availableOptions[0].value
      } else {
        // 其他状态（认领等）直接使用默认值
        formData.value.principalType = 'INDIVIDUAL_BUSINESS'
      }

      isLicenseInfoExtracted.value = false
    }
  })

  // 初始化数据
  onMounted(async () => {
    await init()
    // 初始化完成后，标记初始化完成，允许watch正常工作
    isInitializationComplete.value = true
  })

  return {
    // 数据
    formData,
    formRules,
    isEdit,
    isPreview,
    isLicenseInfoExtracted,
    // poiId,

    // 营业类型相关
    businessTypeSheetVisible,
    businessTypeText,
    businessTypePickerColumns,
    businessTypePickerValue,

    // 方法
    formatValidityPeriod,
    showBusinessTypeSheet,
    hideBusinessTypeSheet,
    confirmBusinessTypePicker,
    handleLicenseInfoExtracted,
    handleDraftAlert,

    // 专业号资质相关
    getAndSetEffectiveQualification,
    isProfessionalQualificationEditable,

    // loading状态
    isInitializing,
    isProfessionalQualificationLoading,

    // 草稿状态检测
    showDraftAlert,

    // 新增：主体类型
    subjectType,
  }
}
