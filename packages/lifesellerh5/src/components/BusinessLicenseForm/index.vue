<template>
  <div class="business-license-form">
    <!-- 营业执照信息标题 -->
    <div class="section-title">营业资质</div>

    <!-- 初始化loading遮罩 -->
    <div v-if="isInitializing">
      <LoadingNext />
    </div>

    <div class="form-container" :class="{ 'form-loading': isInitializing || isProfessionalQualificationLoading }">
      <Form
        ref="baseFormRef"
        :data="formData"
        :rules="formRules"
        :align="FormType.ALIGN.RIGHT"
        :show-submit="false"
        @submit="submitSuccess"
        @error="submitError"
      >
        <!-- 使用专业号资质 -->
        <FormItem
          v-if="canUseProfessionalQualification"
          label="使用专业号资质"
          name="useProfessionalQualification"
          :layout="FormType.LAYOUT.VERTICAL"
          :value="formData.useProfessionalQualification"
          class="form-item form-item-first"
          :required-style="{color: '#FF2442'}"
        >
          <div class="qualification-switch">
            <div class="qualification-description">使用后，自动读取专业号申请时提交的营业执照</div>
            <Switch
              v-model="formData.useProfessionalQualification"
              :disabled="isPreview || isInitializing"
              size="24px"
              style="transform: translateY(-80%);"
            />
          </div>
          <RejectReasonDisplay
            v-if="isSettle"
            field-code="useProfessionalQualification"
            :current-value="formData.useProfessionalQualification"
            :original-value="originalFormData?.useProfessionalQualification"
          />
        </FormItem>

        <!-- 营业类型 -->
        <FormItem
          v-if="!isLightClaim"
          label="营业类型"
          name="principalType"
          :value="formData.principalType"
          class="form-item  form-item-first"
          :required-style="{color: '#FF2442'}"
        >
          <div class="selector" :style="{ 'min-width': formData.principalType ? 'auto' : '100px' }" @click="showBusinessTypeSheet">
            <Text>{{ businessTypeText }}</Text>
            <OnixIcon
              v-if="!isPreview && isProfessionalQualificationEditable && !isInitializing && !isProfessionalQualificationLoading"
              class="onix-icon-16"
              icon="arrowRightRightM"
              :style="{ opacity: (!isProfessionalQualificationEditable || isInitializing || isProfessionalQualificationLoading) ? 0.4 : 1 }"
            />
          </div>
        </FormItem>

        <!-- 营业执照 -->
        <FormItem
          label="营业执照"
          name="businessLicense"
          :value="formData.businessLicense"
          :layout="FormType.LAYOUT.VERTICAL"
          class="form-item form-item-last-settle"
          :required-style="{color: '#FF2442'}"
        >
          <BusinessLicenseUpload
            v-model="formData.businessLicense"
            :is-disabled="isPreview || !isProfessionalQualificationEditable || isInitializing || isProfessionalQualificationLoading"
            :is-light-claim="isLightClaim"
            :business-type="formData.principalType"
            @info-extracted="handleLicenseInfoExtracted"
          />
          <RejectReasonDisplay
            v-if="isSettle"
            field-code="merchantCompanyDraft.businessLicense.fileAttachmentList"
            :current-value="formData.businessLicense"
            :original-value="originalFormData?.businessLicense"
          />
        </FormItem>

        <!-- 上传营业执照解析过一次后显示 -->
        <template v-if="isLicenseInfoExtracted && !isLightClaim">
          <!-- 企业名称 -->
          <FormItem
            label="企业名称"
            name="companyName"
            :value="formData.companyName"
            class="form-item"
            :required-style="{color: '#FF2442'}"
          >
            <TextView
              v-model="formData.companyName"
              :is-form-item="true"
              type="area"
              clear-type="icon"
              :max-length="50"
              :min-rows="1"
              :max-rows="3"
              :clearable="!!formData.companyName"
              :disabled="!isProfessionalQualificationEditable || isPreview || isProfessionalQualificationLoading"
              :style="{
                color: 'rgba(0, 0, 0, 0.62)',
                fontFamily: 'PingFang SC',
                fontSize: '16px',
                fontWeight: '400',
                textAlign: 'right'
              }"
            />
            <RejectReasonDisplay
              v-if="isSettle"
              field-code="merchantCompanyDraft.companyName"
              :current-value="formData.companyName"
              :original-value="originalFormData?.companyName"
            />
          </FormItem>

          <!-- 统一社会信用代码 -->
          <FormItem
            label="统一社会信用代码"
            :label-width="150"
            name="uniformCreditCode"
            :value="formData.uniformCreditCode"
            class="form-item"
            :required-style="{color: '#FF2442'}"
          >
            <TextView
              v-model="formData.uniformCreditCode"
              :is-form-item="true"
              type="textarea"
              :max-length="18"
              clear-type="icon"
              :clearable="!!formData.uniformCreditCode"
              :disabled="!isProfessionalQualificationEditable || isPreview || isProfessionalQualificationLoading"
              :style="{
                color: 'rgba(0, 0, 0, 0.62)',
                fontFamily: 'PingFang SC',
                fontSize: '16px',
                fontWeight: '400',
                textAlign: 'right'
              }"
              :min-rows="1"
              :max-rows="3"
            />
            <RejectReasonDisplay
              v-if="isSettle"
              field-code="merchantCompanyDraft.uniformCreditCode"
              :current-value="formData.uniformCreditCode"
              :original-value="originalFormData?.uniformCreditCode"
            />
          </FormItem>

          <!-- 经营地址 -->
          <FormItem
            label="经营地址"
            name="businessAddress"
            :value="formData.businessAddress"
            class="form-item"
            :required-style="{color: '#FF2442'}"
          >
            <TextView
              v-model="formData.businessAddress"
              :is-form-item="true"
              :max-length="100"
              clear-type="icon"
              :style="{
                color: 'rgba(0, 0, 0, 0.62)',
                fontFamily: 'PingFang SC',
                fontSize: '16px',
                fontWeight: '400',
                textAlign: 'right'
              }"
              :clearable="!!formData.businessAddress"
              :disabled="!isProfessionalQualificationEditable || isPreview || isProfessionalQualificationLoading"
              :min-rows="1"
              :max-rows="5"
            />
            <RejectReasonDisplay
              v-if="isSettle"
              field-code="merchantCompanyDraft.companyRegisteredAddress"
              :current-value="formData.businessAddress"
              :original-value="originalFormData?.businessAddress"
            />
          </FormItem>

          <!-- 有效期 -->
          <FormItem
            label="有效期"
            name="validityPeriod"
            :value="formatValidityPeriod(formData.validityPeriod)"
            class="form-item form-item-last-settle"
            :required-style="{color: '#FF2442'}"
          >
            <ValidityTimePicker
              v-model="formData.validityPeriod"
              :disabled="!isProfessionalQualificationEditable || isPreview || isProfessionalQualificationLoading"
            />
          </FormItem>
        </template>
      </Form>
    </div>

    <!-- 营业类型选择弹窗 -->
    <Picker
      :visible="businessTypeSheetVisible"
      :columns="businessTypePickerColumns"
      :value="businessTypePickerValue"
      :close-type="SheetsType.SheetsActionType.text"
      :cancel-type="SheetsType.SheetsActionType.text"
      :z-index="1000"
      :label="'请选择营业类型'"
      :cancel-text="'取消'"
      @confirm="confirmBusinessTypePicker"
      @cancel="hideBusinessTypeSheet"
    />

    <!-- 草稿检测弹窗 -->
    <Alert
      title="检测到存在未提交草稿，是否继续编辑？"
      :show="showDraftAlert"
      confirm-text="确认"
      cancel-text="取消"
      message=""
      footer-layout="horizontal"
      @confirm="() => handleDraftAlert(true)"
      @cancel="() => handleDraftAlert(false)"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { useStore } from 'vuex'
  import {
    Form,
    FormItem,
    FormType,
    Text,
    Picker,
    SheetsType,
    Switch,
    TextView,
    Alert
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'
  import BusinessLicenseUpload from './BusinessLicenseUpload.vue'
  import ValidityTimePicker from './ValidityTimePicker/index'
  import LoadingNext from '../loading-next/index.vue'

  import { useBusinessLicenseForm } from './useBaseBusinessLicenseForm'
  import { ClaimTypeEnum } from '~/enum/shop'
  import RejectReasonDisplay from '../RejectReasonDisplay/index.vue'
  import '~/assets/svg/arrowRightRightM.svg'

  // Props 定义
  interface Props {
    canUseProfessionalQualification?: boolean // 是否可以使用专业号资质
    externalFormData?: any // 外部传入的表单数据（如果传入则使用外部数据）
    isLightClaim?: boolean // 是否为轻认领
    isSettle?: boolean // 是否为入驻模式
  }

  // Events 定义
  interface Emits {
    (e: 'update:formData', data: any): void
    (e: 'submit-success', success: boolean): void
    (e: 'submit-error', info: any): void
  }

  const props = withDefaults(defineProps<Props>(), {
    canUseProfessionalQualification: true,
    isLightClaim: false,
    isSettle: false
  })
  const emit = defineEmits<Emits>()

  const store = useStore()

  // 判断是否为轻认领 - 优先使用props，如果没有则从store计算
  const isLightClaim = computed(() => {
    // 如果props中明确指定了isLightClaim，则使用props
    if (props.isLightClaim !== undefined) {
      return props.isLightClaim
    }
    // 否则从store计算
    return store.state.ShopUserInfoStore.customClaimType === ClaimTypeEnum.LIGHT_CLAIM
  })
  // const isUseProCertification

  // 如果外部传入了 formData，则使用外部的；否则使用组件内部的
  const businessLicenseFormHook = useBusinessLicenseForm({
    props
  })

  // 统一的 formData 引用 - 确保始终有值，避免 undefined 错误
  const formData = computed(() => {
    // 如果有外部数据且值存在，使用外部数据的value
    if (props.externalFormData && props.externalFormData.value) {
      return props.externalFormData.value
    }
    // 否则使用内部 hook 的数据的value
    return businessLicenseFormHook.formData.value
  })

  // 从 rejectReason store 获取原始表单数据
  const originalFormData = computed(() => store?.getters?.['rejectReason/businessLicenseOriginalFormData'] || {})

  const {
    formRules,
    isPreview,
    isProfessionalQualificationEditable,
    isLicenseInfoExtracted,
    businessTypeSheetVisible,
    businessTypeText,
    businessTypePickerColumns,
    businessTypePickerValue,
    isInitializing,
    isProfessionalQualificationLoading,
    showDraftAlert,
    showBusinessTypeSheet, confirmBusinessTypePicker, hideBusinessTypeSheet, formatValidityPeriod, handleLicenseInfoExtracted, handleDraftAlert
  } = businessLicenseFormHook

  // 安全的监听 formData 变化
  watch(() => formData.value, newFormData => {
    if (newFormData) {
      // console.log('BusinessLicenseForm 内部数据变化:', newFormData)
      emit('update:formData', newFormData)
    }
  }, { deep: true })

  const submitSuccess = () => {
    // 提交成功后的处理逻辑
    // console.log('Form submitted successfully')
    emit('submit-success', true)
  }

  const submitError = (error: any) => {
    // console.log('Form submission failed:', error)
    // 提交失败后的处理逻辑
    emit('submit-error', error)
  }

  const baseFormRef = ref()
  const validateForm = async () => {
    if (!baseFormRef.value) return false
    // console.log('Validating form...')
    await baseFormRef.value.handleSubmit()
  }

  defineExpose({
    validateForm,
    baseFormRef,
    isLicenseInfoExtracted, // 暴露 isLicenseInfoExtracted
    formData: businessLicenseFormHook.formData // 暴露内部的 formData
  })
</script>

<style lang="stylus" scoped>
.business-license-form
  width 100%
  position relative

// 表单样式
.form-container
  border-radius 8px
  margin-bottom 20px
  padding 0 0 0 16px
  background #FFF
  .form-item
    padding 12px 16px 12px 0
    margin-bottom 0
    border-bottom 0.5px solid rgba(0, 0, 0, 0.08)
    &:last-child
      border-bottom none

    // 第一个表单项的右上角圆角
    &.form-item-first
      border-top-right-radius 8px

    // 认领模式最后一个表单项的右下角圆角
    &.form-item-last-claim
      border-bottom-right-radius 8px

    // 入驻模式最后一个表单项的右下角圆角
    &.form-item-last-settle
      border-bottom-right-radius 8px

.selector
  display flex
  align-items center
  justify-content space-between
  cursor pointer
  color rgba(0, 0, 0, 0.62),
  font-size 16px
  font-style normal
  font-weight 400

.section-title
  margin 0 0 12px 16px
  color rgba(0, 0, 0, 0.62)
  font-family "PingFang SC"
  font-size 14px
  font-style normal
  font-weight 400
  line-height 20px

.field-placeholder
  color rgba(0, 0, 0, 0.45)
  font-family "PingFang SC"
  font-size 16px
  font-style normal
  font-weight 400

.qualification-switch
  display flex
  align-items center
  justify-content space-between
  transform translateY(-8px)
  // 专业号资质描述
  .qualification-description
    color rgba(0, 0, 0, 0.45)
    font-size 12px
    line-height 17px

.onix-icon-16
  width 16px
  height 16px

.form-loading
  opacity 0.6
  pointer-events none

</style>
