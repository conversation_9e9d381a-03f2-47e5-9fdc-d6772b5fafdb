<template>
  <div class="validity-time-picker-wrapper">
    <!-- 显示区域 -->
    <div
      class="validity-time-picker-display"
      :class="{ 'readonly': readOnly, 'disabled': disabled }"
      @click="handleDisplayClick"
    >
      <slot name="display" :value="displayValue" :formatted="formattedValue">
        <div class="display-content">
          <div class="display-right">
            <span v-if="formattedValue" class="display-text">
              {{ formattedValue }}
            </span>
            <span v-else class="display-placeholder">
              {{ placeholder }}
            </span>
            <OnixIcon
              v-if="!readOnly && !disabled"
              class="display-icon"
              icon="arrowRightRightM"
            />
          </div>
        </div>
      </slot>
    </div>

    <!-- 有效期时间选择弹窗 -->
    <ConfigProvider :color-mode="colorMode">
      <Sheets
        :visible="visible"
        :label="label"
        :cancel="true"
        :close="true"
        :close-type="SheetsType.SheetsActionType.icon"
        :line="false"
        :z-index="1000"
        :is-mask-close="false"
        :header-style="{
          background: 'rgba(245, 245, 245, 1.00)',
        }"
        @cancel="backToValidityTypeSelection"
        @confirm="handleConfirm"
      >
        <div class="validity-time-picker-content">
          <!-- 第一步：期限类型选择 -->
          <div v-if="!showDateRangeSelection" class="validity-type-section">
            <ConfigProvider :color-mode="colorMode">
              <RadioGroup
                v-model="radioSelection"
                size="medium"
                @change="handleValidityTypeChange"
              >
                <div class="radio-item">
                  <Radio :name="true">
                    永久有效
                  </Radio>
                  <Divider direction="horizontal" :margin="[10, 0]" />
                  <Radio :name="false">
                    期限有效
                  </Radio>
                </div>
              </RadioGroup>
            </ConfigProvider>
          </div>

          <!-- 第二步：日期范围选择界面 -->
          <div v-if="showDateRangeSelection" class="date-range-section">
            <!-- 日期选择区域 - 水平布局 -->
            <div class="date-range-column">
              <!-- 起始日期选择 -->
              <div class="date-item-row">
                <div class="date-label">起始日期</div>
                <div class="date-picker-container">
                  <div class="date-display" @click="showStartDatePicker">
                    <span v-if="value?.startTime" class="date-text">{{ value?.startTime }}</span>
                    <span v-else class="date-placeholder">请选择起始日期</span>
                    <OnixIcon class="date-icon" icon="arrowRightRightM" />
                  </div>
                </div>
              </div>

              <!-- 结束日期选择 -->
              <div class="date-item-row">
                <div class="date-label">结束日期</div>
                <div class="date-picker-container">
                  <div class="date-display" @click="showEndDatePicker">
                    <span v-if="value?.endTime" class="date-text">{{ value?.endTime }}</span>
                    <span v-else class="date-placeholder">请选择结束日期</span>
                    <OnixIcon class="date-icon" icon="arrowRightRightM" />
                  </div>
                </div>
              </div>
            </div>

            <!-- 提示文字 -->
            <div class="validity-tip">
              提交的营业执照有效期剩余时间应大于30天
            </div>

            <!-- 错误提示 -->
            <div v-if="errorMessage" class="error-message">
              {{ errorMessage }}
            </div>

            <!-- 确认按钮 -->
            <Button
              v-if="showDateRangeSelection"
              type="primary"
              class="confirm-button"
              @click="handleDateRangeConfirm"
            >
              确认
            </Button>
          </div>
        </div>

      </Sheets>

      <!-- 日期选择器 -->
      <DatePicker
        :value="currentDateType === 'start' ? (value?.startTime || '') : (value?.endTime || '')"
        :visible="datePickerVisible"
        :label="currentDateType === 'start' ? '选择起始日期' : '选择结束日期'"
        :start-time="getDateRange().startTime"
        :end-time="getDateRange().endTime"
        :z-index="1001"
        :is-mask-close="false"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
      />
    </ConfigProvider>
  </div>
</template>

<script setup lang="ts">
  // 组件库
  import {
    Sheets,
    SheetsType,
    RadioGroup,
    Radio,
    DatePicker,
    ConfigProvider,
    Divider,
    Button
  } from '@xhs/reds-h5-next'
  import OnixIcon from '@xhs/onix-icon'

  // 本地模块
  import { useValidityTimePicker } from './composable'
  import { ValidityTimePickerValue } from './types'

  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'

  // Props 接口定义
  interface Props {
    /** 双向绑定的值 */
    modelValue?: ValidityTimePickerValue | null
    /** 是否只读 */
    readOnly?: boolean
    /** 占位符文本 */
    placeholder?: string
    /** 弹窗标题 */
    label?: string
    /** 颜色模式 */
    colorMode?: 'light' | 'dark'
    /** 日期格式化字符串 */
    dateFormat?: string
    /** 是否禁用 */
    disabled?: boolean
  }

  // Props
  const props = withDefaults(defineProps<Props>(), {
    modelValue: null,
    readOnly: false,
    disabled: false,
    placeholder: '请选择时间段',
    label: '选择有效期',
    colorMode: 'light',
    dateFormat: 'YYYY-MM-DD'
  })

  // Emits
  const emits = defineEmits<{
    'update:modelValue': [value: ValidityTimePickerValue | null]
    'change': [value: ValidityTimePickerValue | null]
  }>()

  // 使用组合式函数
  const {
    visible,
    datePickerVisible,
    value,
    radioSelection,
    displayValue,
    formattedValue,
    errorMessage,
    // 界面状态
    showDateRangeSelection,
    currentDateType,
    // 事件处理
    handleDisplayClick,
    handleConfirm,
    handleValidityTypeChange,
    backToValidityTypeSelection,
    showStartDatePicker,
    showEndDatePicker,
    handleDateConfirm,
    handleDateCancel,
    handleDateRangeConfirm,
    // 验证方法
    valid
  } = useValidityTimePicker(props, emits)

  // 获取日期选择器的时间范围（前后20年）
  const getDateRange = () => {
    const now = new Date()
    const currentYear = now.getFullYear()
    const currentMonth = String(now.getMonth() + 1).padStart(2, '0')
    const currentDay = String(now.getDate()).padStart(2, '0')

    return {
      startTime: `${currentYear - 20}-${currentMonth}-${currentDay}`,
      endTime: `${currentYear + 20}-${currentMonth}-${currentDay}`
    }
  }

  // 暴露方法给父组件
  defineExpose({
    valid
  })
</script>

<style lang="stylus" scoped>
.validity-time-picker-wrapper
  width 100%
  font-size: 16px;
.validity-time-picker-display
  background: rgba(255, 255, 255, 1.00);

.display-content
  display flex
  align-items center
  justify-content space-between
  width 100%
  height 100%

.display-right
  display flex
  align-items center
  justify-content flex-end
  flex 1
  color: rgba(0, 0, 0, 0.62);

.display-text
  font-weight: 400;
  line-height: 24px;

.display-placeholder
  font-size: 16px;
  text-align: right;
  font-weight: 400;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.27);

.display-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0

/* 弹窗内容样式 */
.validity-time-picker-content
  padding 10px 16px
  background: rgba(245, 245, 245, 1.00);

.validity-type-section
  margin-bottom 20px

.radio-item
  width 100%
  padding 16px
  border-radius 8px
  background rgba(255, 255, 255, 1.00)
  display flex
  flex-direction column

/* 日期范围选择界面样式 */
.date-range-section
  .date-range-header
    display flex
    align-items center
    margin-bottom 20px
    position relative

  .back-button
    display flex
    align-items center
    gap 4px
    background none
    border none
    cursor pointer
    padding 8px
    border-radius 4px
    color rgba(0, 0, 0, 0.80)
    font-size 14px

    &:hover
      background rgba(0, 0, 0, 0.05)

  .back-icon
    width 16px
    height 16px

  .back-text
    font-weight 400

  .date-range-title
    position absolute
    left 50%
    transform translateX(-50%)
    margin 0
    color rgba(0, 0, 0, 0.80)
    font-size 16px
    font-weight 500

// 水平布局的日期选择行
.date-range-column
  display flex
  flex-direction column
  gap 12px
  background: rgba(255, 255, 255, 1.00);
  padding 12px 16px
  border-radius 8px

.date-item-row
  flex 1
  display flex
  align-items center
  justify-content space-between
  gap 12px

.date-item
  margin-bottom 16px
  background: rgba(255, 255, 255, 1.00);

  .date-label
    color rgba(0, 0, 0, 0.80)
    font-size 14px
    font-weight 500
    margin 0 0 8px 0

.date-picker-container
  .date-display
    width 100%
    display flex
    align-items center
    justify-content space-between

.date-text
  line-height 24px

.date-placeholder
  color rgba(0, 0, 0, 0.40)
  font-weight 400
  line-height 24px

.date-icon
  width 16px
  height 16px
  color rgba(0, 0, 0, 0.60)
  flex-shrink 0

/* 提示文字样式 */
.validity-tip
  color: var(--Light-Labels-Description, rgba(0, 0, 0, 0.45));
  /* Caption/C2 */
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px; /* 150% */
  margin-bottom 24px

/* 错误信息样式 */
.error-message
  font-size 12px
  color #ff4d4f
  line-height 20px
  margin-bottom 16px
  text-align left

/* 确认按钮样式 */
.confirm-button
  width 100%
  margin-top 8px
</style>
