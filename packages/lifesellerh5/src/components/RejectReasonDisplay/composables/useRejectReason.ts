import { computed, ref, watch } from 'vue'
import { useStore } from 'vuex'
import { IRejectDetailList } from '~/services/edith_get_deposit_detail'

/**
 * 匹配策略枚举
 */
export enum MatchStrategy {
  /** 精确匹配 fieldCode */
  EXACT = 'exact',
  /** 模糊匹配，忽略数组ID部分 */
  FUZZY = 'fuzzy',
  /** 前缀匹配 */
  PREFIX = 'prefix',
  /** 模块匹配 */
  MODULE = 'module',
  /** 组合匹配：moduleCode + fieldCode前缀 */
  COMBINED = 'combined'
}

/**
 * 查询条件接口
 */
export interface IRejectQuery {
  /** 模块编码 */
  moduleCode?: string
  /** 字段编码 */
  fieldCode?: string
  /** 字段名称 */
  fieldName?: string
  /** 匹配策略 */
  strategy?: MatchStrategy
  /** 原始数据 */
  originalData?: any
  /** 当前数据 */
  currentData?: any
}

/**
 * 清理 fieldCode，移除数组ID部分
 * 例如：merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList*1097420507324764160.fileAttachmentList
 * 变为：merchantCategoryDraft.categoryQualificationMap.qualificationList.fileAttachmentList
 */
const cleanFieldCode = (fieldCode: string): string => {
  if (!fieldCode) return ''
  return fieldCode.replace(/\*[^.]+/g, '')
}

/**
 * 获取 fieldCode 的前缀部分
 * 例如：merchantBaseDraft.sellerName -> merchantBaseDraft
 */
const getFieldCodePrefix = (fieldCode: string): string => {
  if (!fieldCode) return ''
  return fieldCode.split('.')[0]
}

/**
 * 转换 fieldCode - 参考PC端逻辑处理特殊情况
 */
const transformFieldCode = (fieldCode: string): string => {
  if (!fieldCode) return ''

  // 字段映射规则 - 参考PC端逻辑
  const fieldMappings = {
    // 店铺信息映射
    'merchantBaseDraft.sellerName': 'shopInfo.sellerName',
    'merchantBaseDraft.sellerLogoUrl': 'shopInfo.sellerLogoUrl',
    'merchantBaseDraft.sellerPrefixName': 'shopInfo.sellerPrefixName',

    // 运营人信息映射
    'merchantBaseDraft.sellerManagerName': 'sellerManager.sellerManagerName',
    'merchantBaseDraft.sellerManagerEmail': 'sellerManager.sellerManagerEmail',
    'merchantBaseDraft.wechat': 'sellerManager.wechat',

    // 营业执照信息映射
    'merchantCompanyDraft.useProfessionalQualification': 'useProfessionalQualification',
    'merchantCompanyDraft.principalType': 'principalType',
    'merchantCompanyDraft.businessLicense': 'businessLicense',
    'merchantCompanyDraft.uniformCreditCode': 'uniformCreditCode',
    'merchantCompanyDraft.companyName': 'companyName',
    'merchantCompanyDraft.businessAddress': 'businessAddress',
    'merchantCompanyDraft.validityPeriod': 'validityPeriod'
  }

  // 直接映射
  if (fieldMappings[fieldCode]) {
    return fieldMappings[fieldCode]
  }

  // 处理身份证照片 - 移除后缀
  if (fieldCode.includes('merchantAuthorizationDraft.qualificationList')) {
    return 'merchantAuthorizationDraft.qualificationList'
  }

  // 处理营业执照照片 - 移除后缀
  if (fieldCode.includes('merchantCompanyDraft.businessLicense')) {
    return 'merchantCompanyDraft.businessLicense'
  }

  // 处理类目资质 - 移除后缀
  if (fieldCode.includes('merchantCategoryDraft.categoryQualificationMap')) {
    return 'merchantCategoryDraft.categoryQualificationMap'
  }

  // 处理门店认领
  if (fieldCode.includes('poiInfo.')) {
    return 'poiInfo'
  }

  // 处理营业执照相关字段
  if (fieldCode.startsWith('merchantCompanyDraft.')) {
    return fieldCode.replace('merchantCompanyDraft.', '')
  }

  return fieldCode
}

/**
 * 匹配拒绝详情的工具函数
 */
const matchRejectDetail = (item: IRejectDetailList, query: IRejectQuery): boolean => {
  const {
    moduleCode,
    fieldCode,
    fieldName,
    strategy = MatchStrategy.EXACT
  } = query

  switch (strategy) {
    case MatchStrategy.EXACT: {
      // 精确匹配
      return !!(
        (!moduleCode || item.moduleCode === moduleCode)
        && (!fieldCode || item.fieldCode === fieldCode)
        && (!fieldName || item.fieldName === fieldName)
      )
    }

    case MatchStrategy.FUZZY: {
      // 模糊匹配，忽略数组ID
      const itemCleanCode = cleanFieldCode(item.fieldCode || '')
      const queryCleanCode = cleanFieldCode(fieldCode || '')
      const itemTransformedCode = transformFieldCode(itemCleanCode)
      const queryTransformedCode = transformFieldCode(queryCleanCode)

      return !!(
        (!moduleCode || item.moduleCode === moduleCode)
        && (!fieldCode || itemTransformedCode === queryTransformedCode || itemCleanCode === queryCleanCode)
        && (!fieldName || item.fieldName === fieldName)
      )
    }

    case MatchStrategy.PREFIX: {
      // 前缀匹配
      return !!(
        (!moduleCode || item.moduleCode === moduleCode)
        && (!fieldCode || (item.fieldCode && item.fieldCode.startsWith(fieldCode)))
        && (!fieldName || (item.fieldName && item.fieldName.includes(fieldName)))
      )
    }

    case MatchStrategy.MODULE: {
      // 仅模块匹配
      return item.moduleCode === moduleCode
    }

    case MatchStrategy.COMBINED: {
      // 组合匹配：moduleCode + fieldCode前缀
      const itemPrefix = getFieldCodePrefix(item.fieldCode || '')
      const queryPrefix = getFieldCodePrefix(fieldCode || '')
      return !!(
        item.moduleCode === moduleCode
        && (!fieldCode || itemPrefix === queryPrefix)
      )
    }

    default:
      return false
  }
}

/**
 * 检查数据是否发生变化
 */
const isDataChanged = (originalData: any, currentData: any): boolean => {
  if (originalData === currentData) return false
  if (originalData === undefined || currentData === undefined) return true

  // 处理数组
  if (Array.isArray(originalData) && Array.isArray(currentData)) {
    if (originalData.length !== currentData.length) return true
    return originalData.some((item, index) => isDataChanged(item, currentData[index]))
  }

  // 处理对象
  if (typeof originalData === 'object' && originalData !== null
      && typeof currentData === 'object' && currentData !== null) {
    const keys = Object.keys(originalData)
    if (keys.length !== Object.keys(currentData).length) return true
    return keys.some(key => isDataChanged(originalData[key], currentData[key]))
  }

  // 处理基本类型
  return originalData !== currentData
}

/**
 * 增强版拒绝原因 composable
 * @param query 查询条件或简单的moduleCode字符串
 * @returns 拒绝原因相关的响应式数据和方法
 */
export const useRejectReason = (query: IRejectQuery | string) => {
  const store = useStore()

  // 标准化查询条件
  const normalizedQuery = computed(() => (
    typeof query === 'string'
      ? { moduleCode: query, strategy: MatchStrategy.MODULE }
      : { strategy: MatchStrategy.EXACT, ...query }
  ))

  // 是否正在编辑状态 - 根据store中的id判断
  const isEditing = computed(() => !!store.state.ShopApplyStore?.shopApplyState?.id)

  // 字段值变更标记
  const isFieldUpdated = ref(false)

  // 从 store 获取拒绝详情列表
  const rejectDetailList = computed(() => store.getters['rejectReason/rejectDetailList'] as IRejectDetailList[])

  // 根据查询条件筛选匹配的拒绝原因
  const matchedRejectDetails = computed(() => {
    if (!rejectDetailList.value) return []

    return rejectDetailList.value.filter(item =>
      matchRejectDetail(item, normalizedQuery.value))
  })

  // 是否有拒绝原因且需要显示
  const hasRejectReason = computed(() => matchedRejectDetails.value.length > 0
    && isEditing.value
    && !isFieldUpdated.value)

  // 拒绝原因文本内容
  const rejectReasonText = computed(() => {
    if (!hasRejectReason.value) return ''

    return matchedRejectDetails.value
      .map(item => item.rejectContent)
      .filter(content => content && content.trim())
      .join('; ')
  })

  // 拒绝原因详情列表
  const rejectReasonDetails = computed(() => {
    if (!hasRejectReason.value) return []

    return matchedRejectDetails.value
      .filter(item => item.rejectContent && item.rejectContent.trim())
      .map(item => ({
        fieldName: item.fieldName || '',
        rejectContent: item.rejectContent || '',
        fieldCode: item.fieldCode || '',
        moduleCode: item.moduleCode || '',
        transformedFieldCode: transformFieldCode(cleanFieldCode(item.fieldCode || ''))
      }))
  })

  // 标记字段已更新
  const markFieldUpdated = () => {
    isFieldUpdated.value = true
  }

  // 重置状态 - 只重置字段更新标记
  const resetRejectState = () => {
    isFieldUpdated.value = false
  }

  // 监听 rejectDetailList 变化
  watch(rejectDetailList, newList => {
    if (!newList || newList.length === 0) {
      resetRejectState()
    }
  })

  // 自动清除错误 - 参考PC端逻辑
  const createFieldWatcher = (fieldPath: string, callback?: () => void) => watch(
    () => fieldPath, // 这里可以扩展为监听实际的表单字段值
    () => {
      const matchedField = matchedRejectDetails.value.find(detail => {
        const transformedCode = transformFieldCode(cleanFieldCode(detail.fieldCode || ''))
        return transformedCode === fieldPath || detail.fieldCode?.includes(fieldPath)
      })

      if (matchedField) {
        markFieldUpdated()
        callback?.()
      }
    }
  )

  // 显示拒绝原因 - 参考PC端逻辑
  const showRejectReasons = () => matchedRejectDetails.value
    .filter(detail => !detail.fieldCode?.includes('*')) // 跳过数组元素
    .map(detail => ({
      fieldCode: detail.fieldCode || '',
      transformedFieldCode: transformFieldCode(cleanFieldCode(detail.fieldCode || '')),
      rejectContent: detail.rejectContent || '',
      fieldName: detail.fieldName || '',
      errorMessage: `驳回原因：${detail.rejectContent || ''}`
    }))

  // 批量清除指定字段的拒绝原因
  const clearFieldRejectReasons = (fieldPaths: string[]) => {
    const hasMatched = fieldPaths.some(fieldPath => matchedRejectDetails.value.some(detail => {
      const transformedCode = transformFieldCode(cleanFieldCode(detail.fieldCode || ''))
      return transformedCode === fieldPath || detail.fieldCode?.includes(fieldPath)
    }))

    if (hasMatched) {
      markFieldUpdated()
    }

    return hasMatched
  }

  return {
    // 响应式数据
    isEditing,
    isFieldUpdated: computed(() => isFieldUpdated.value),
    hasRejectReason,
    rejectReasonText,
    rejectReasonDetails,
    matchedRejectDetails,

    // 方法
    markFieldUpdated,
    resetRejectState,
    createFieldWatcher,
    showRejectReasons,
    clearFieldRejectReasons,

    // 工具方法
    transformFieldCode: (fieldCode: string) => transformFieldCode(fieldCode),
    cleanFieldCode: (fieldCode: string) => cleanFieldCode(fieldCode)
  }
}

/**
 * 拒绝原因查询器 - 提供多种便捷的查询方法
 */
export const useRejectReasonQuery = () => {
  const store = useStore()

  const rejectDetailList = computed(() => store.getters['rejectReason/rejectDetailList'] as IRejectDetailList[])

  return {
    rejectDetailList,

    /**
     * 根据精确的 fieldCode 查找
     */
    getByExactFieldCode: (fieldCode: string) =>
      rejectDetailList.value.filter(item => item.fieldCode === fieldCode),

    /**
     * 根据模糊的 fieldCode 查找（忽略数组ID）
     */
    getByFuzzyFieldCode: (fieldCode: string) =>
      rejectDetailList.value.filter(item => cleanFieldCode(item.fieldCode || '') === cleanFieldCode(fieldCode)),

    /**
     * 根据 fieldCode 前缀查找
     */
    getByFieldCodePrefix: (prefix: string) =>
      rejectDetailList.value.filter(item => item.fieldCode && item.fieldCode.startsWith(prefix)),

    /**
     * 根据 moduleCode 查找
     */
    getByModuleCode: (moduleCode: string) =>
      rejectDetailList.value.filter(item => item.moduleCode === moduleCode),

    /**
     * 组合查询：moduleCode + fieldCode前缀
     */
    getByCombined: (moduleCode: string, fieldCodePrefix: string) =>
      rejectDetailList.value.filter(item =>
        item.moduleCode === moduleCode
        && item.fieldCode
        && getFieldCodePrefix(item.fieldCode) === fieldCodePrefix),

    /**
     * 根据字段名称模糊查找
     */
    getByFieldName: (fieldName: string) =>
      rejectDetailList.value.filter(item =>
        item.fieldName && item.fieldName.includes(fieldName)),

    /**
     * 自定义查询
     */
    query: (query: IRejectQuery) =>
      rejectDetailList.value.filter(item => matchRejectDetail(item, query)),

    /**
     * 获取所有模块编码
     */
    getAllModuleCodes: () =>
      [...new Set(rejectDetailList.value.map(item => item.moduleCode).filter(Boolean))],

    /**
     * 获取指定模块的所有字段编码前缀
     */
    getFieldCodePrefixesByModule: (moduleCode: string) =>
      [...new Set(
        rejectDetailList.value
          .filter(item => item.moduleCode === moduleCode)
          .map(item => getFieldCodePrefix(item.fieldCode || ''))
          .filter(Boolean)
      )],

    /**
     * 根据fieldCode模糊匹配，返回rejectContent
     * @param fieldCode 字段编码
     * @param joinSeparator 多个结果的连接符，默认为'; '
     * @returns 匹配到的拒绝原因内容
     */
    getRejectContentByFuzzyFieldCode: (fieldCode: string, joinSeparator: string = '; ') => {
      if (!fieldCode) return ''

      const cleanedFieldCode = cleanFieldCode(fieldCode)
      const transformedFieldCode = transformFieldCode(cleanedFieldCode)

      const matchedItems = rejectDetailList.value.filter(item => {
        if (!item.fieldCode) return false

        const itemCleanCode = cleanFieldCode(item.fieldCode)
        const itemTransformedCode = transformFieldCode(itemCleanCode)

        // 多重匹配策略
        return itemTransformedCode === transformedFieldCode
          || itemCleanCode === cleanedFieldCode
          || item.fieldCode === fieldCode
      })

      const rejectContents = matchedItems
        .map(item => item.rejectContent)
        .filter(content => content && content.trim())

      return rejectContents.join(joinSeparator)
    },

    /**
     * 专门用于资质字段的智能匹配，支持复杂的ID匹配逻辑
     * @param fieldCode 字段编码
     * @param joinSeparator 多个结果的连接符，默认为'; '
     * @returns 匹配到的拒绝原因内容
     */
    getRejectContentByQualificationFieldCode: (fieldCode: string, joinSeparator: string = '; ') => {
      if (!fieldCode) return ''

      const matchedItems = rejectDetailList.value.filter(item => {
        if (!item.fieldCode || item.moduleCode !== 'QUALIFICATION') return false

        // 对于资质字段，使用更智能的匹配策略
        if (fieldCode.includes('merchantCategoryDraft.categoryQualificationMap')
            && item.fieldCode.includes('merchantCategoryDraft.categoryQualificationMap')) {
          // 提取关键部分进行匹配
          const extractQualificationInfo = (code: string) => {
            const categoryMatch = code.match(/categoryQualificationMap\*([^.]+)/)
            const qualificationMatch = code.match(/qualificationList\*([^.]+)(?:\.(.+))?$/)
            return {
              categoryId: categoryMatch?.[1],
              qualificationPart: qualificationMatch?.[1],
              suffix: qualificationMatch?.[2]
            }
          }

          const queryInfo = extractQualificationInfo(fieldCode)
          const itemInfo = extractQualificationInfo(item.fieldCode)

          // 检查资质部分的匹配（不依赖categoryId）
          if (queryInfo.qualificationPart && itemInfo.qualificationPart) {
            // 精确匹配
            if (queryInfo.qualificationPart === itemInfo.qualificationPart) {
              return true
            }
            // 模糊匹配（包含关系）
            if (queryInfo.qualificationPart.includes(itemInfo.qualificationPart)
                || itemInfo.qualificationPart.includes(queryInfo.qualificationPart)) {
              return true
            }
          }

          // 如果查询使用通配符（*），则进行更宽松的匹配
          if (fieldCode.includes('categoryQualificationMap*')
              && queryInfo.qualificationPart && itemInfo.qualificationPart) {
            // 对于通配符查询，只要资质部分匹配即可
            if (queryInfo.qualificationPart === itemInfo.qualificationPart) {
              return true
            }
          }
        }

        // 回退到基础匹配
        return item.fieldCode === fieldCode
      })

      const rejectContents = matchedItems
        .map(item => item.rejectContent)
        .filter(content => content && content.trim())

      if (process.env.NODE_ENV === 'development' && rejectContents.length > 0) {
        console.log('[RejectReason] 资质字段匹配成功:', {
          fieldCode,
          matchedCount: matchedItems.length,
          contents: rejectContents
        })
      }

      return rejectContents.join(joinSeparator)
    },

    /**
     * 根据fieldCode和ID精确匹配营业资质等带ID的字段，返回rejectContent
     * @param fieldCode 基础字段编码（不含ID部分）
     * @param id 具体的ID值
     * @param joinSeparator 多个结果的连接符，默认为'; '
     * @returns 匹配到的拒绝原因内容
     */
    getRejectContentByFieldCodeWithId: (fieldCode: string, id: string, joinSeparator: string = '; ') => {
      if (!fieldCode || !id) return ''

      const matchedItems = rejectDetailList.value.filter(item => {
        if (!item.fieldCode) return false

        // 匹配包含指定ID的fieldCode
        // 例如: merchantCategoryDraft.categoryQualificationMap*64240f7b7d13e90001fa7d01.qualificationList
        return item.fieldCode.includes(`${fieldCode}*${id}`)
          || item.fieldCode.includes(`*${id}`)
          || (item.fieldCode.includes(fieldCode) && item.fieldCode.includes(`*${id}`))
      })

      const rejectContents = matchedItems
        .map(item => item.rejectContent)
        .filter(content => content && content.trim())

      return rejectContents.join(joinSeparator)
    },

    /**
     * 根据fieldCode前缀和多个ID匹配，返回rejectContent
     * @param fieldCodePrefix 字段编码前缀
     * @param ids ID数组
     * @param joinSeparator 多个结果的连接符，默认为'; '
     * @returns 匹配到的拒绝原因内容
     */
    getRejectContentByFieldCodeWithIds: (fieldCodePrefix: string, ids: string[], joinSeparator: string = '; ') => {
      if (!fieldCodePrefix || !ids || ids.length === 0) return ''

      const matchedItems = rejectDetailList.value.filter(item => {
        if (!item.fieldCode) return false

        // 检查是否包含字段前缀，并且包含任一指定ID
        return item.fieldCode.includes(fieldCodePrefix)
          && ids.some(id => item.fieldCode && item.fieldCode.includes(`*${id}`))
      })

      const rejectContents = matchedItems
        .map(item => item.rejectContent)
        .filter(content => content && content.trim())

      return rejectContents.join(joinSeparator)
    }
  }
}

/**
 * 表单字段拒绝原因 composable
 */
export const useFormFieldRejectReason = (moduleCode: string, fieldCode: string, originalData?: any, currentData?: any) => {
  const store = useStore()
  const isEditing = computed(() => !!store.state.ShopApplyStore?.shopApplyState?.id)
  const rejectDetailList = computed(() => store.getters['rejectReason/rejectDetailList'] as IRejectDetailList[])

  // 根据查询条件筛选匹配的拒绝原因
  const matchedRejectDetails = computed(() => {
    if (!rejectDetailList.value) return []

    return rejectDetailList.value.filter(item =>
      matchRejectDetail(item, {
        moduleCode,
        fieldCode,
        strategy: MatchStrategy.FUZZY
      }))
  })

  // 是否有拒绝原因且需要显示
  const hasRejectReason = computed(() => {
    // 如果没有在编辑状态，不显示驳回原因
    if (!isEditing.value) return false

    // 如果没有匹配的驳回原因，不显示
    if (matchedRejectDetails.value.length === 0) return false

    // 如果有原始数据和当前数据，且数据发生了变化，不显示驳回原因
    if (originalData !== undefined && currentData !== undefined) {
      return !isDataChanged(originalData, currentData)
    }

    return true
  })

  // 拒绝原因文本内容
  const rejectReasonText = computed(() => {
    if (!hasRejectReason.value) return ''

    return matchedRejectDetails.value
      .map(item => item.rejectContent)
      .filter(content => content && content.trim())
      .join('; ')
  })

  return {
    hasRejectReason,
    rejectReasonText
  }
}
