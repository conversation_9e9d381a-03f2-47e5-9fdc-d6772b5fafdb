<template>
  <div v-if="shouldShowRejectReason" class="reject-reason-display">
    <Text class="reject-reason-text">{{ rejectReasonText }}</Text>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue'
  import { Text } from '@xhs/reds-h5-next'
  import { useRejectReasonQuery } from './composables/useRejectReason'

  // Props 定义
  interface Props {
    /** 字段编码，用于匹配驳回原因 */
    fieldCode: string
    /** 当前字段值 */
    currentValue?: any
    /** 原始字段值，用于比较是否发生变化 */
    originalValue?: any
    /** 是否启用值变化检测，默认为 true */
    enableValueChangeDetection?: boolean
    /** 自定义匹配策略 */
    matchStrategy?: 'exact' | 'fuzzy' | 'prefix' | 'qualification'
  }

  const props = withDefaults(defineProps<Props>(), {
    enableValueChangeDetection: true,
    matchStrategy: 'fuzzy'
  })

  const rejectReasonQuery = useRejectReasonQuery()

  // 标记该字段是否已被用户修改
  const isFieldModified = ref(false)

  // 监听当前值的变化
  watch(
    () => props.currentValue,
    (newValue, oldValue) => {
      if (!props.enableValueChangeDetection) return

      // 如果是首次设置值（从 undefined 到有值），不认为是修改
      if (oldValue === undefined && newValue !== undefined) {
        return
      }

      // 如果值发生了实际变化，标记为已修改
      if (oldValue !== undefined && !isEqual(oldValue, newValue)) {
        isFieldModified.value = true
      }
    }
  )

  // 监听 originalValue 的变化，重置修改状态
  watch(
    () => props.originalValue,
    () => {
      isFieldModified.value = false
    }
  )

  // 深度比较两个值是否相等
  const isEqual = (a: any, b: any): boolean => {
    if (a === b) return true
    if (a == null || b == null) return false
    if (typeof a !== typeof b) return false

    if (typeof a === 'object') {
      if (Array.isArray(a) && Array.isArray(b)) {
        if (a.length !== b.length) return false
        return a.every((item, index) => isEqual(item, b[index]))
      }

      if (Array.isArray(a) || Array.isArray(b)) return false

      const keysA = Object.keys(a)
      const keysB = Object.keys(b)
      if (keysA.length !== keysB.length) return false

      return keysA.every(key => isEqual(a[key], b[key]))
    }

    return false
  }

  // 获取驳回原因文本
  const rejectReasonText = computed(() => {
    if (!props.fieldCode) return ''

    switch (props.matchStrategy) {
      case 'exact':
        return rejectReasonQuery.getByExactFieldCode(props.fieldCode)
          .map(item => item.rejectContent)
          .filter(content => content && content.trim())
          .join('; ')

      case 'prefix':
        return rejectReasonQuery.getByFieldCodePrefix(props.fieldCode)
          .map(item => item.rejectContent)
          .filter(content => content && content.trim())
          .join('; ')

      case 'qualification':
        // 专门用于资质字段的智能匹配
        return rejectReasonQuery.getRejectContentByQualificationFieldCode(props.fieldCode)

      case 'fuzzy':
      default:
        // 对于资质相关字段，优先使用资质专用匹配策略
        if (props.fieldCode.includes('merchantCategoryDraft.categoryQualificationMap')) {
          const qualificationResult = rejectReasonQuery.getRejectContentByQualificationFieldCode(props.fieldCode)
          if (qualificationResult) {
            return qualificationResult
          }
        }
        return rejectReasonQuery.getRejectContentByFuzzyFieldCode(props.fieldCode)
    }
  })

  // 是否应该显示驳回原因
  const shouldShowRejectReason = computed(() => {
    // 必须有驳回原因内容
    if (!rejectReasonText.value) return false

    // 如果没有原始数据，说明不是编辑状态，不显示驳回原因
    if (props.originalValue === undefined || props.originalValue === null) {
      return false
    }

    // 如果启用了值变化检测且字段已被修改，则不显示
    if (props.enableValueChangeDetection && isFieldModified.value) return false

    // 如果有原始值和当前值，且它们不相等，则不显示
    if (props.currentValue !== undefined) {
      if (!isEqual(props.originalValue, props.currentValue)) {
        return false
      }
    }

    return true
  })

  // 暴露方法给父组件
  const resetModifiedState = () => {
    isFieldModified.value = false
  }

  const forceHide = () => {
    isFieldModified.value = true
  }

  defineExpose({
    resetModifiedState,
    forceHide,
    isFieldModified: computed(() => isFieldModified.value),
    shouldShowRejectReason
  })
</script>

<style lang="stylus" scoped>
.reject-reason-display
  margin-top 8px

.reject-reason-text
  color #ff4d4f;
  font-size 12px
  line-height 17px
  font-weight 400
  word-break break-word
</style>
