<template>
  <div class="legal-person-form">
    <!-- 法人信息标题 -->
    <div class="section-title">法人信息</div>
    <div class="form-container">
      <Form
        ref="legalBaseFormRef"
        :data="formData"
        :rules="formRules"
        :align="FormType.ALIGN.RIGHT"
        :show-submit="false"
        @submit="submitSuccess"
        @error="submitError"
      >
        <!-- 姓名 -->
        <FormItem
          label="姓名"
          name="name"
          :value="formData.name"
          class="form-item form-item-first"
          :required-style="{color: '#FF2442'}"
        >
          <TextField
            v-model="formData.name"
            input-align="right"
            placeholder="请输入姓名"
            :disabled="false"
            class="text-field"
            :input-style="{
              color: 'rgba(0, 0, 0, 0.62)'
            }"
            style="padding: 0 !important;"
            @focus="handleFocus('name')"
          />
          <RejectReasonDisplay
            :original-value="originalFormData.name"
            :current-value="formData.name"
            field-code="merchantAuthorizationDraft.authorizationName"
          />

        </FormItem>

        <!-- 证件类型 -->
        <FormItem
          label="证件类型"
          name="idType"
          :value="formData.idType"
          class="form-item"
          :required-style="{color: '#FF2442'}"
        >
          <div class="selector" @click="showIdTypeSheet">
            <Text v-if="idTypeText" class="text-style">{{ idTypeText }}</Text>
            <Text v-else class="selector-placeholder">请选择</Text>
            <OnixIcon
              v-if="!isPreview"
              class="onix-icon-16"
              icon="arrowRightRightM"
            />
          </div>
          <RejectReasonDisplay
            v-if="isSettle"
            :original-value="originalFormData.idType"
            :current-value="formData.idType"
            field-code="merchantAuthorizationDraft.certificateType"
          />
        </FormItem>

        <!-- 上传证件 -->
        <FormItem
          label="上传证件"
          name="idImages"
          :value="formData.idImages"
          :layout="FormType.LAYOUT.VERTICAL"
          class="form-item form-item-last"
          :required-style="{color: '#FF2442'}"
        >
          <IdCardUpload
            v-model="formData.idImages"
            :user-name="formData.name"
            :is-disabled="isPreview"
            @info-extracted="handleInfoExtracted"
          />
          <RejectReasonDisplay
            v-if="isSettle"
            :original-value="originalFormData.idImages?.[0] || []"
            :current-value="formData.idImages?.[0] || []"
            field-code="merchantAuthorizationDraft.qualificationList*889821130321592320.fileAttachmentList"
          />
          <RejectReasonDisplay
            v-if="isSettle"
            :original-value="originalFormData.idImages?.[1] || []"
            :current-value="formData.idImages?.[1] || []"
            field-code="merchantAuthorizationDraft.qualificationList*889821308189442048.fileAttachmentList"
          />
        </FormItem>

        <!-- 解析后的证件号码 -->
        <!-- 上传身份证并解析后才展示下面内容 -->
        <template v-if="isIdParsed">
          <FormItem
            :label="idNumberLabel"
            name="idNumber"
            :value="formData.idNumber"
            class="form-item"
            :required-style="{color: '#FF2442'}"
          >
            <TextField
              v-model="formData.idNumber"
              :placeholder="`请输入${idNumberLabel}`"
              :clearable="!!formData.idNumber"
              :disabled="isPreview"
              input-align="right"
              class="text-field"
              :input-style="{
                color: 'rgba(0, 0, 0, 0.62)'
              }"
              style="padding: 0 !important;"
              @focus="handleFocus('idNumber')"
            />
            <RejectReasonDisplay
              v-if="isSettle"
              :original-value="originalFormData.idNumber"
              :current-value="formData.idNumber"
              field-code="merchantAuthorizationDraft.certificateNumber"
            />
          </FormItem>

          <!-- 有效期选择 -->
          <FormItem
            label="有效期"
            name="validityPeriod"
            class="form-item form-item-last"
            :required-style="{color: '#FF2442'}"
          >
            <ValidityTimePicker
              v-model="formData.validityPeriod"
              :read-only="isPreview"
              :disabled="isPreview"
            />
          </FormItem>

          <!-- 额外表单字段插槽 -->
          <slot
            name="extra-fields"
            :form-data="formData"
            :is-preview="isPreview"
          />
        </template>
      </Form>
      <!-- 证件类型选择弹窗 -->
      <Picker
        :columns="idTypePickerColumns"
        :value="idTypePickerValue"
        :close-type="SheetsType.SheetsActionType.text"
        :cancel-type="SheetsType.SheetsActionType.text"
        :label="'请选择证件类型'"
        :z-index="1000"
        :visible="idTypeSheetVisible"
        @confirm="confirmIdTypePicker"
        @cancel="hideIdTypeSheet"
      />
    </div>

    <!-- 底部插槽 -->
    <slot
      name="footer"
      :form-data="formData"
      :is-preview="isPreview"
    />
  </div>
</template>

<script setup lang="ts">

  import {
    Form,
    FormItem,
    FormType,
    TextField,
    Text,
    SheetsType,
    Picker
  } from '@xhs/reds-h5-next'
  import { computed, ref } from 'vue'
  import { useStore } from 'vuex'

  import OnixIcon from '@xhs/onix-icon'

  // 组件
  import IdCardUpload from './IdCardUpload.vue'
  import ValidityTimePicker from './ValidityTimePicker/index'
  import RejectReasonDisplay from '../RejectReasonDisplay/index.vue'

  import { useBaseLegalPersonForm } from './useBaseLegalPersonForm'

  // 类型定义
  import type { LegalPersonData } from './useBaseLegalPersonForm'

  import '~/assets/svg/arrowRightRightM.svg'

  // Props
  interface Props {
    formData?: LegalPersonData // 外部传入的表单数据（可选）
    formRules: Record<string, any>
    isPreview: boolean
    isIdParsed?: boolean // 添加 isIdParsed 作为可选 prop
    handleInfoExtracted?: (info: any) => void // 可选的OCR信息处理函数
    disabled?: boolean
    isSettle?: boolean // 是否为入驻场景
  }
  interface Emits {
    (e: 'update:formData', data: any): void
    (e: 'submit-success', success: boolean): void
    (e: 'submit-error', info: any): void
    (e: 'focus', name: string): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()
  const store = useStore()
  const originalFormData = computed(() => store?.getters?.['rejectReason/legalPersonOriginalFormData'] || {})

  // 如果外部传入了 formData，则使用外部的；否则使用组件内部的
  const legalPersonFormHook = useBaseLegalPersonForm()

  // 统一的 formData 引用
  const formData = props.formData || legalPersonFormHook.formData

  // 使用传入的 isIdParsed，如果没有传入则使用内部的
  const isIdParsedFromProps = computed(() => props.isIdParsed)
  const isIdParsed = props.isIdParsed !== undefined ? isIdParsedFromProps : legalPersonFormHook.isIdParsed

  const {
    idTypeSheetVisible,
    idTypePickerColumns,
    idTypePickerValue,
    confirmIdTypePicker,
    hideIdTypeSheet,

    idTypeText, idNumberLabel, showIdTypeSheet, handleInfoExtracted: defaultHandleInfoExtracted
  } = legalPersonFormHook

  // 使用传入的handleInfoExtracted，如果没有则使用默认的
  const handleInfoExtracted = props.handleInfoExtracted || defaultHandleInfoExtracted

  const submitSuccess = () => {
    // 提交成功后的处理逻辑
    // console.log('Form submitted successfully')
    emit('submit-success', true)
  }

  const submitError = (error: any) => {
    // console.log('Form submission failed:', error)
    // 提交失败后的处理逻辑
    emit('submit-error', error)
  }

  const handleFocus = (name: string) => {
    if (!props.isSettle) {
      emit('focus', name)
    }
  }

  const legalBaseFormRef = ref()
  const validateForm = async () => {
    // console.log('validateForm', formRef.value)
    if (!legalBaseFormRef.value) return false
    await legalBaseFormRef.value.handleSubmit()
  }

  defineExpose({
    validateForm
  })

</script>

<style lang="stylus" scoped>
.onix-icon-16
  width 16px
  height 16px

// 表单样式
.form-container
  border-radius 8px
  margin-bottom 8px
  padding 0 0 0 16px
  background #FFF

  .form-item
    padding 12px 16px 12px 0
    margin-bottom 0
    border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

    &:last-child
      border-bottom none

    // 第一个表单项的右上角圆角
    &.form-item-first
      border-top-right-radius 8px

    // 认领模式最后一个表单项的右下角圆角
    &.form-item-last
      border-bottom-right-radius 8px

:deep(.selector)
  display flex
  align-items center
  justify-content space-between
  cursor pointer
  .text-style
    font-size 16px
    font-weight 400
    line-height 24px
    color rgba(0, 0, 0, 0.62)
  .selector-placeholder
    font-size 16px
    font-weight 400
    line-height 24px
    color rgba(0, 0, 0, 0.62)

// 验证码容器
:deep(.verify-code-container)
  display flex
  align-items center
  gap 12px

// 协议
:deep(.agreement-section)
  display flex
  padding 5px

  .agreement-text
    display flex
    align-items center
    font-size 12px
    margin-left -8px

    span
      color #133667
      cursor pointer

.section-title
  margin 0 0 12px 16px
  color rgba(0, 0, 0, 0.62)
  font-family "PingFang SC"
  font-size 14px
  font-style normal
  font-weight 400
  line-height 20px
</style>
