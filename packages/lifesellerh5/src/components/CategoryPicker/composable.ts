import { ref, computed, watch } from 'vue'
import {
  getMarketableCategories
} from '~/services/edith_get_marketable_categories'
import {
  getChildrenCategories,
  IPathInfo
} from '~/services/edith_get_children_categories'

// 内联类型定义，避免外部类型导入的编译问题
interface ICategory {
  id?: string
  name?: string
  isLeaf?: boolean
  level?: number
  parentId?: string // 添加parentId字段
}

interface CategoryItem {
  id: string
  name: string
}

// 修改类型定义：modelValue现在只是最后一层的id
type CategoryPickerValue = string

interface LevelData {
  level: number
  categories: ICategory[]
  selectedId?: string
  selectedIds?: string[]
}

interface CategoryPickerProps {
  modelValue?: CategoryPickerValue | null // 现在只是string类型
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  title?: string
  maxLevels?: number
  lastLevelMultiple?: boolean
  gridColumns?: number
}

interface CategoryPickerEmits {
  (e: 'update:modelValue', value: CategoryPickerValue): void // 现在只传出最后一层id
  (e: 'change', value: CategoryPickerValue): void
  (e: 'confirm', value: CategoryPickerValue): void
}

export function useCategoryPicker(props: CategoryPickerProps, emit: CategoryPickerEmits) {
  // 响应式状态
  const loading = ref(false)
  const error = ref('')
  const visible = ref(false)

  // 数据格式转换函数 - 将 API 返回的数据转换为组件内部使用的格式
  const transformCategoryData = (rawData: any[]): ICategory[] => rawData.map(item => ({
    id: item.id,
    name: item.name,
    level: item.level,
    isLeaf: String(item.is_leaf || item.isLeaf) === 'true', // 统一转换为 boolean
    parentId: item.parentId
  }))

  // 级联数据 - 支持多层级
  const levelsData = ref<LevelData[]>([])
  const currentLevel = ref(0) // 当前展示的层级

  // 内部维护的完整路径数组（不对外暴露）
  const internalPath = ref<CategoryItem[]>([])

  // 当前选中的最后一层id
  const currentValue = ref<string>('')

  // 最大层级数
  const maxLevels = computed(() => props.maxLevels || 3)

  // 网格列数
  const gridColumns = computed(() => props.gridColumns || 3)

  // 缓存最后一次的显示文本，避免重复调用接口
  const cachedDisplayText = ref<string>('')

  // 根据API返回的paths数据构建分类路径（优化版本）
  const buildCategoryPathFromPaths = (paths: IPathInfo[], selectedCategoryName?: string): CategoryItem[] => {
    const result: CategoryItem[] = []

    // 将API返回的paths转换为CategoryItem格式
    paths.forEach(pathItem => {
      if (pathItem.id && pathItem.name) {
        result.push({
          id: pathItem.id,
          name: pathItem.name
        })
      }
    })

    // 如果有选中的分类名称，添加到路径的最后
    if (selectedCategoryName && result.length > 0) {
      // 检查最后一个路径项是否就是选中的分类
      const lastItem = result[result.length - 1]
      if (lastItem.name !== selectedCategoryName) {
        // 注意：这里需要根据实际的选中分类ID来添加，暂时使用name作为标识
        // 实际使用时可能需要从childrenInfos中找到对应的分类信息
        result.push({
          id: '', // 这里需要从其他地方获取实际的ID
          name: selectedCategoryName
        })
      }
    }

    return result
  }

  // 优化后的buildCategoryPath函数 - 优先使用paths数据
  const buildCategoryPath = async (categoryId: string): Promise<CategoryItem[]> => {
    if (!categoryId) return []

    try {
      // 尝试通过getChildrenCategories获取包含paths的数据
      // 注意：这里假设可以通过某种方式获取到包含paths的数据
      // 实际实现可能需要调用不同的API或者修改现有API

      // 如果API支持获取paths数据，优先使用paths
      const response = await getChildrenCategories({ categoryId })

      if (response.paths && response.paths.length > 0) {
        // 使用paths数据快速构建路径，并找出选中的最后一层分类
        const paths = buildCategoryPathFromPaths(response.paths)

        // 如果有childrenInfos，从中找到选中的分类并添加到路径最后
        if (response.childrenInfos && response.childrenInfos.length > 0) {
          const selectedCategory = response.childrenInfos.find(item => item.id === categoryId)
          if (selectedCategory && selectedCategory.name && selectedCategory.id) {
            paths.push({
              id: selectedCategory.id,
              name: selectedCategory.name
            })
          }
        }

        return paths
      }

      // 如果没有paths数据，回退到原来的逻辑
      return await buildCategoryPathLegacy(categoryId)
    } catch (err) {
      console.error('构建分类路径失败:', err)
      return []
    }
  }

  // 原有的buildCategoryPath逻辑（作为备用方案）
  const buildCategoryPathLegacy = async (categoryId: string): Promise<CategoryItem[]> => {
    if (!categoryId) return []

    try {
      // 首先加载第一层数据
      await initializeData()

      const allCategories: ICategory[] = []

      // 收集所有层级的分类数据
      const collectAllCategories = async (parentId: string | undefined = undefined, level: number = 1) => {
        if (parentId) {
          const response = await getChildrenCategories({ categoryId: parentId })
          const rawCategories = response.childrenInfos || []
          const categories = transformCategoryData(rawCategories)

          categories.forEach(cat => {
            allCategories.push({
              ...cat,
              level,
              parentId
            })
          })

          // 继续加载子级分类
          // 注意：叶子节点也可以继续请求子节点，只有达到最大层级才停止
          if (level < maxLevels.value) {
            const subTasks = categories
              .map(cat => collectAllCategories(cat.id, level + 1))
            await Promise.all(subTasks)
          }
        } else {
          const response = await getMarketableCategories({})
          const rawCategories = response.itemInfos || []
          const categories = transformCategoryData(rawCategories)

          categories.forEach(cat => {
            allCategories.push({
              ...cat,
              level,
              parentId
            })
          })

          // 继续加载子级分类
          // 注意：叶子节点也可以继续请求子节点，只有达到最大层级才停止
          if (level < maxLevels.value) {
            const subTasks = categories
              .map(cat => collectAllCategories(cat.id, level + 1))
            await Promise.all(subTasks)
          }
        }
      }

      // 收集所有分类数据
      await collectAllCategories()

      // 从目标id开始，向上构建路径
      const buildPath = (targetId: string): CategoryItem[] => {
        const reversePath: CategoryItem[] = []
        let currentId = targetId

        while (currentId) {
          const searchId = currentId
          const category = allCategories.find(cat => cat.id === searchId)
          if (!category) break

          reversePath.unshift({
            id: category.id || '',
            name: category.name || ''
          })

          currentId = category.parentId || ''
        }

        return reversePath
      }

      return buildPath(categoryId)
    } catch (err) {
      console.error('构建分类路径失败:', err)
      return []
    }
  }

  // 增强版的loadLevelsFromPath - 支持paths数据的层级回显
  const loadLevelsFromPathWithPaths = async (path: CategoryItem[], pathsData?: IPathInfo[]) => {
    try {
      loading.value = true
      error.value = ''

      // 清空现有数据
      levelsData.value = []

      // 如果有paths数据，可以更高效地构建层级数据
      if (pathsData && pathsData.length > 0) {
        // 加载第一层数据
        const firstResponse = await getMarketableCategories({})
        const rawFirstLevelCategories = firstResponse.itemInfos || []
        const firstLevelCategories = transformCategoryData(rawFirstLevelCategories)

        levelsData.value.push({
          level: 1,
          categories: firstLevelCategories,
          selectedId: pathsData[0]?.id || path[0]?.id || undefined,
          selectedIds: undefined
        })

        // 根据paths数据逐层加载
        for (let i = 0; i < pathsData.length - 1; i++) {
          const currentPathItem = pathsData[i]
          if (currentPathItem.id) {
            try {
              // eslint-disable-next-line no-await-in-loop
              const childResponse = await getChildrenCategories({ categoryId: currentPathItem.id })
              const rawChildCategories = childResponse.childrenInfos || []
              const childCategories = transformCategoryData(rawChildCategories)

              if (childCategories.length > 0) {
                const nextPathItem = pathsData[i + 1]
                levelsData.value.push({
                  level: i + 2,
                  categories: childCategories,
                  selectedId: nextPathItem?.id || undefined,
                  selectedIds: undefined
                })
              }
            } catch (err) {
              console.warn(`加载层级 ${i + 2} 数据失败:`, err)
              break
            }
          }
        }

        // 加载最后一层的数据（如果需要）
        const lastPathItem = pathsData[pathsData.length - 1]
        if (lastPathItem && lastPathItem.id && !lastPathItem.is_leaf) {
          try {
            const lastResponse = await getChildrenCategories({ categoryId: lastPathItem.id })
            const rawLastCategories = lastResponse.childrenInfos || []
            const lastCategories = transformCategoryData(rawLastCategories)

            if (lastCategories.length > 0) {
              levelsData.value.push({
                level: pathsData.length + 1,
                categories: lastCategories,
                selectedId: undefined, // 最后一层根据实际选中的分类来设置
                selectedIds: undefined
              })

              // 如果path中有更多层级，设置最后一层的选中状态
              if (path.length > pathsData.length) {
                const lastLevelData = levelsData.value[levelsData.value.length - 1]
                const targetCategory = path[path.length - 1]
                if (targetCategory) {
                  lastLevelData.selectedId = targetCategory.id
                }
              }
            }
          } catch (err) {
            console.warn('加载最后一层数据失败:', err)
          }
        }
      } else {
        // 没有paths数据时，使用原来的逻辑
        await loadLevelsFromPathOriginal(path)
      }

      // 更新内部路径
      internalPath.value = path
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 原始的loadLevelsFromPath函数（作为备用）
  const loadLevelsFromPathOriginal = async (path: CategoryItem[]) => {
    // 清空现有数据
    levelsData.value = []

    // 加载第一层数据
    const response = await getMarketableCategories({})
    const rawFirstLevelCategories = response.itemInfos || []
    const firstLevelCategories = transformCategoryData(rawFirstLevelCategories)

    levelsData.value.push({
      level: 1,
      categories: firstLevelCategories,
      selectedId: path[0]?.id || undefined,
      selectedIds: undefined
    })

    // 逐层加载后续数据
    for (let i = 0; i < path.length - 1 && i < maxLevels.value - 1; i++) {
      const currentCategoryId = path[i].id
      if (!currentCategoryId) {
        break
      }

      // eslint-disable-next-line no-await-in-loop
      const childResponse = await getChildrenCategories({ categoryId: currentCategoryId })
      const rawChildCategories = childResponse.childrenInfos || []
      const childCategories = transformCategoryData(rawChildCategories)

      if (childCategories.length > 0) {
        levelsData.value.push({
          level: i + 2,
          categories: childCategories,
          selectedId: path[i + 1]?.id || undefined,
          selectedIds: undefined
        })
      }
    }
  }

  // 更新loadLevelsFromPath函数，使其支持paths数据
  const loadLevelsFromPath = loadLevelsFromPathWithPaths

  // 初始化数据 - 优化版本
  const initializeData = async () => {
    try {
      loading.value = true
      error.value = ''

      const response = await getMarketableCategories({})

      if (!response) {
        throw new Error('API 响应为空')
      }

      const rawCategories = response.itemInfos || []
      const categories = transformCategoryData(rawCategories)

      // 初始化第一层数据
      levelsData.value = [{
        level: 1,
        categories,
        selectedId: undefined,
        selectedIds: undefined
      }]

      // 如果有数据，使用智能自动选择逻辑
      if (categories.length > 0) {
        await autoSelectFirstOptions()
      }

      currentLevel.value = 0
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 智能自动选择逻辑 - 只在层级没有选中数据时自动选择第一个
  const autoSelectFirstOptions = async () => {
    let currentLevelIndex = 0

    while (currentLevelIndex < maxLevels.value && currentLevelIndex < levelsData.value.length) {
      const currentLevelData = levelsData.value[currentLevelIndex]

      if (currentLevelData.categories.length === 0) break

      // 检查当前层级是否已有选中项
      if (!currentLevelData.selectedId) {
        // 只有在没有选中项时，才自动选中第一个选项
        const firstCategory = currentLevelData.categories[0]
        currentLevelData.selectedId = firstCategory.id
      }

      // 获取当前层级的选中分类
      const selectedCategory = currentLevelData.categories.find(cat => cat.id === currentLevelData.selectedId)

      if (!selectedCategory) break

      // 如果已达到最大层级，停止
      // 注意：叶子节点也可以继续请求子节点，只有达到最大层级才停止
      if (currentLevelIndex + 1 >= maxLevels.value) {
        break
      }

      // 基于当前选中的分类加载下一层数据
      try {
        // eslint-disable-next-line no-await-in-loop
        const response = await getChildrenCategories({ categoryId: selectedCategory.id || '' })

        if (response.childrenInfos && response.childrenInfos.length > 0) {
          const rawChildrenCategories = response.childrenInfos
          const transformedCategories = transformCategoryData(rawChildrenCategories)

          const nextLevelData: LevelData = {
            level: currentLevelIndex + 2,
            categories: transformedCategories,
            selectedId: undefined, // 下一层不自动选择，等待用户选择或者通过paths数据设置
            selectedIds: undefined
          }

          if (levelsData.value.length <= currentLevelIndex + 1) {
            levelsData.value.push(nextLevelData)
          } else {
            levelsData.value[currentLevelIndex + 1] = nextLevelData
          }

          currentLevelIndex += 1
        } else {
          // 没有子分类，停止
          break
        }
      } catch (err) {
        // 加载失败，停止并记录错误（但不影响主流程）
        console.warn('智能自动选择时加载子分类失败:', err)
        break
      }
    }

    // 自动选择完成后，同步更新 currentValue
    updateCurrentValueFromLevelsData()
  }

  // 新增：基于现有选中状态加载子集数据
  const loadChildrenBasedOnSelection = async () => {
    // 如果已经在加载中，避免重复加载
    if (loading.value) return

    try {
      loading.value = true
      error.value = ''

      for (let i = 0; i < levelsData.value.length; i++) {
        const currentLevelData = levelsData.value[i]

        // 如果当前层级有选中项，且不是叶子节点，且下一层没有数据，则加载子集
        if (currentLevelData.selectedId) {
          const selectedCategory = currentLevelData.categories.find(cat => cat.id === currentLevelData.selectedId)

          if (selectedCategory && i + 1 < maxLevels.value) {
            // 检查下一层是否已有数据
            const nextLevelIndex = i + 1
            const hasNextLevelData = levelsData.value.length > nextLevelIndex
              && levelsData.value[nextLevelIndex].categories.length > 0

            if (!hasNextLevelData) {
              // 下一层没有数据，基于当前选中项加载子集
              try {
                // eslint-disable-next-line no-await-in-loop
                const response = await getChildrenCategories({ categoryId: selectedCategory.id || '' })

                if (response.childrenInfos && response.childrenInfos.length > 0) {
                  const rawChildrenCategories = response.childrenInfos
                  const transformedCategories = transformCategoryData(rawChildrenCategories)

                  const nextLevelData: LevelData = {
                    level: nextLevelIndex + 1,
                    categories: transformedCategories,
                    selectedId: undefined, // 不自动选择
                    selectedIds: undefined
                  }

                  if (levelsData.value.length <= nextLevelIndex) {
                    levelsData.value.push(nextLevelData)
                  } else {
                    levelsData.value[nextLevelIndex] = nextLevelData
                  }
                }
              } catch (err) {
                console.warn(`基于选中状态加载第 ${nextLevelIndex + 1} 层数据失败:`, err)
              }
            }
          }
        }
      }
    } catch (err: any) {
      error.value = err.message || '加载子集数据失败'
    } finally {
      loading.value = false
    }
  }

  // 根据初始值加载对应的子分类 - 优化版本，支持paths数据
  const initializeFromValue = async (value: string) => {
    if (!value) return

    try {
      loading.value = true
      error.value = ''

      // 尝试通过getChildrenCategories获取包含paths的完整数据
      let pathsData: IPathInfo[] | undefined
      let categoryPath: CategoryItem[] = []

      try {
        // 尝试获取包含paths的数据
        const response = await getChildrenCategories({ categoryId: value })

        if (response.paths && response.paths.length > 0) {
          // 使用paths数据
          pathsData = response.paths
          categoryPath = buildCategoryPathFromPaths(response.paths)

          // 添加当前选中的分类到路径末尾
          if (response.childrenInfos && response.childrenInfos.length > 0) {
            const selectedCategory = response.childrenInfos.find(item => item.id === value)
            if (selectedCategory && selectedCategory.name && selectedCategory.id) {
              categoryPath.push({
                id: selectedCategory.id,
                name: selectedCategory.name
              })
            }
          }
        }
      } catch (err) {
        console.warn('获取paths数据失败，使用备用方案:', err)
      }

      // 如果没有获取到paths数据，使用原来的逻辑
      if (!pathsData || pathsData.length === 0) {
        categoryPath = await buildCategoryPath(value)
      }

      if (categoryPath.length > 0) {
        // 根据路径和paths数据加载层级数据
        await loadLevelsFromPath(categoryPath, pathsData)
      }

      // 更新当前层级到最后一个有数据的层级
      currentLevel.value = Math.max(0, levelsData.value.length - 1)
    } catch (err: any) {
      error.value = err.message || '初始化分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 仅用于获取回显显示文本的轻量级初始化
  const initializeDisplayTextFromValue = async (value: string) => {
    if (!value) return

    try {
      // 尝试获取包含paths的数据用于回显显示
      const response = await getChildrenCategories({ categoryId: value })

      if (response.paths && response.paths.length > 0) {
        // 使用paths数据构建内部路径用于显示
        const categoryPath = buildCategoryPathFromPaths(response.paths)

        // 添加当前选中的分类到路径末尾
        if (response.childrenInfos && response.childrenInfos.length > 0) {
          const selectedCategory = response.childrenInfos.find(item => item.id === value)
          if (selectedCategory && selectedCategory.name && selectedCategory.id) {
            categoryPath.push({
              id: selectedCategory.id,
              name: selectedCategory.name
            })
          }
        }

        // 更新内部路径，用于displayText显示
        internalPath.value = categoryPath
      }
    } catch (err) {
      console.warn('获取回显数据失败:', err)
      // 如果获取失败，至少设置一个基本的显示文本
      if (value) {
        cachedDisplayText.value = '已选择分类'
      }
    }
  }

  // 监听外部值变化 - 优化：支持立即回显
  watch(
    () => props.modelValue,
    async val => {
      currentValue.value = val || ''

      // 清除缓存的显示文本，如果值变化了
      if (!val) {
        cachedDisplayText.value = ''
        internalPath.value = []
        levelsData.value = []
        return
      }

      // 如果浮层已打开，加载完整数据
      if (visible.value) {
        await initializeFromValue(val)
      } else {
        // 如果浮层未打开，只获取回显显示所需的基础数据
        await initializeDisplayTextFromValue(val)
      }
    },
    { immediate: true }
  )

  // 判断指定层级是否为最后一层
  const isLastLevelByNumber = (level: number): boolean => level === maxLevels.value

  // 是否支持多选（只有最后一层支持）
  const isMultipleMode = computed(() => props.lastLevelMultiple)

  // 优化的显示文本逻辑 - 支持缓存，避免不必要的接口调用
  const displayText = computed(() => {
    // 优先使用内部路径的最后一项显示
    if (internalPath.value.length > 0) {
      const lastItem = internalPath.value[internalPath.value.length - 1]
      const text = lastItem?.name || props.placeholder || '请选择'
      cachedDisplayText.value = text
      return text
    }

    // 如果没有内部路径，尝试从levelsData中获取最后一层的选中项
    if (levelsData.value.length > 0) {
      // 从最后一层开始查找选中的分类
      for (let i = levelsData.value.length - 1; i >= 0; i--) {
        const levelData = levelsData.value[i]
        if (levelData.selectedId) {
          const selectedCategory = levelData.categories.find(cat => cat.id === levelData.selectedId)
          if (selectedCategory?.name) {
            const text = selectedCategory.name
            cachedDisplayText.value = text
            return text
          }
        }
      }
    }

    // 如果有缓存的显示文本且当前有modelValue，优先使用缓存
    if (cachedDisplayText.value && props.modelValue) {
      return cachedDisplayText.value
    }

    return props.placeholder || '请选择'
  })

  // 构建分类值 - 返回最后一层的id
  const buildCategoryValue = (): string => {
    // 找到最后一个有选中项的层级
    for (let i = levelsData.value.length - 1; i >= 0; i--) {
      const levelData = levelsData.value[i]
      if (levelData.selectedId) {
        return levelData.selectedId
      }
    }
    return ''
  }

  // 根据 levelsData 的选中状态更新 currentValue 和 internalPath
  const updateCurrentValueFromLevelsData = () => {
    const newValue = buildCategoryValue()
    currentValue.value = newValue

    // 更新内部路径
    const path: CategoryItem[] = []
    levelsData.value.forEach(levelData => {
      if (levelData.selectedId) {
        const category = levelData.categories.find(cat => cat.id === levelData.selectedId)
        if (category && category.id && category.name) {
          path.push({
            id: category.id,
            name: category.name
          })
        }
      }
    })
    internalPath.value = path
  }

  // 完成选择
  const completeSelection = (value: string) => {
    currentValue.value = value
    emit('update:modelValue', value)
    emit('change', value)
  }

  // 处理多选
  const handleMultipleSelection = (category: ICategory, levelData: LevelData) => {
    if (!levelData.selectedIds) {
      levelData.selectedIds = []
    }

    const selectedIds = levelData.selectedIds
    const categoryId = category.id || ''
    const index = selectedIds.indexOf(categoryId)

    if (index > -1) {
      // 取消选择
      selectedIds.splice(index, 1)
    } else {
      // 添加选择
      selectedIds.push(categoryId)
    }

    // 更新当前值
    updateCurrentValueFromLevelsData()
  }

  // 处理分类项点击（用于非最后一层）
  const handleCategoryClick = async (category: ICategory, levelData: LevelData) => {
    // 如果是最后一层且支持多选，使用多选逻辑
    if (isLastLevelByNumber(levelData.level) && isMultipleMode.value) {
      handleMultipleSelection(category, levelData)
      return
    }

    // 单选模式：如果点击的是当前层级已经选中的节点
    if (levelData.selectedId === category.id) {
      // 如果是最后一层，允许取消选择（适配CheckBox行为）
      if (isLastLevelByNumber(levelData.level)) {
        levelData.selectedId = undefined
        // 🔧 修复：添加状态同步
        updateCurrentValueFromLevelsData()
      }
      return
    }

    // 如果已达到最大层级，直接设置选中项（不需要加载后续层级）
    if (isLastLevelByNumber(levelData.level)) {
      // 最后一层：清空同层级其他选项，只选中当前项
      levelData.selectedId = category.id
      // 清空多选状态（如果有的话）
      levelData.selectedIds = undefined
      // 🔧 修复：添加状态同步
      updateCurrentValueFromLevelsData()
      return
    }

    try {
      loading.value = true
      error.value = ''

      // 单选模式 - 清除当前层级之后的所有层级数据
      const currentLevelIndex = levelsData.value.findIndex(ld => ld.level === levelData.level)
      if (currentLevelIndex >= 0) {
        // 清除后续层级的选择状态和数据
        levelsData.value = levelsData.value.slice(0, currentLevelIndex + 1)
      }

      // 设置当前层级的选中项
      levelData.selectedId = category.id

      // 🔧 修复：立即同步状态
      updateCurrentValueFromLevelsData()

      // 如果未达到最大层级，继续加载并自动选择后续层级
      // 注意：叶子节点也可以继续请求子节点，只有最后一层才不请求
      if (maxLevels.value === 0 || levelData.level < maxLevels.value) {
        await loadAndAutoSelectNextLevels(category.id || '', levelData.level)
        // 🔧 修复：加载完成后再次同步状态
        updateCurrentValueFromLevelsData()
      }
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 加载并自动选择后续层级
  const loadAndAutoSelectNextLevels = async (categoryId: string, currentLevel: number) => {
    // 注意：这个函数通常在 handleCategoryClick 中调用，已经设置了 loading 状态
    // 但为了安全起见，我们在这里也进行状态检查和错误处理
    let parentId = categoryId
    let level = currentLevel

    while (level < maxLevels.value) {
      const nextLevel = level + 1

      // 检查是否达到最大层级限制
      if (maxLevels.value > 0 && nextLevel > maxLevels.value) {
        break
      }

      try {
        // eslint-disable-next-line no-await-in-loop
        const response = await getChildrenCategories({ categoryId: parentId })

        if (!response.childrenInfos || response.childrenInfos.length === 0) {
          // 没有子分类，停止
          break
        }

        // 转换数据格式
        const rawChildrenCategories = response.childrenInfos
        const transformedCategories = transformCategoryData(rawChildrenCategories)

        // 创建下一层数据
        const nextLevelData: LevelData = {
          level: nextLevel,
          categories: transformedCategories,
          selectedId: undefined,
          selectedIds: undefined
        }

        // 添加到层级数据中
        levelsData.value.push(nextLevelData)

        // 自动选中第一个选项，以便继续级联加载
        const firstCategory = transformedCategories[0]
        nextLevelData.selectedId = firstCategory.id

        // 如果已达到最大层级，停止继续加载
        // 注意：叶子节点也可以继续请求子节点，只有达到最大层级才停止
        if (maxLevels.value > 0 && nextLevel >= maxLevels.value) {
          break
        }

        // 准备下一轮循环 - 使用自动选中的分类ID作为下一轮的parentId
        parentId = firstCategory.id || ''
        level = nextLevel
      } catch (err) {
        // 加载失败，停止并设置错误信息
        console.warn('加载子分类失败:', err)
        error.value = '加载子分类失败'
        break
      }
    }

    // 🔧 修复：自动选择完成后同步状态（注意：这个调用会在 handleCategoryClick 中再次调用，但这样更安全）
    // updateCurrentValueFromLevelsData()
  }

  // 返回上一层
  const goBack = () => {
    if (currentLevel.value > 0) {
      currentLevel.value -= 1
      // 清除当前层及之后的选择状态
      for (let i = currentLevel.value + 1; i < levelsData.value.length; i += 1) {
        levelsData.value[i].selectedId = undefined
        levelsData.value[i].selectedIds = undefined
      }
      // 移除后续层级数据
      levelsData.value = levelsData.value.slice(0, currentLevel.value + 1)
    }
  }

  // 确认选择
  const handleConfirm = () => {
    const value = buildCategoryValue()

    // 执行双向绑定
    completeSelection(value)

    // 触发 confirm 事件
    emit('confirm', value)

    visible.value = false
  }

  // 取消选择
  const handleCancel = () => {
    visible.value = false

    // 恢复到确认过的状态，不保存临时修改
    if (props.modelValue) {
      // 如果有确认过的值，恢复到那个状态
      currentValue.value = props.modelValue
    } else {
      // 如果没有确认过的值，清空临时状态
      currentValue.value = ''
    }

    // 清空层级数据，下次打开时重新加载
    levelsData.value = []
    currentLevel.value = 0
  }

  // 重试加载数据
  const retryLoad = async () => {
    try {
      loading.value = true
      error.value = ''
      await initializeData()
    } catch (err: any) {
      error.value = err.message || '重试加载失败'
    } finally {
      loading.value = false
    }
  }

  // 优化的showPicker函数 - 添加基于选中状态的子集加载
  const showPicker = async () => {
    if (props.disabled || props.readonly) {
      return
    }

    // 先打开浮层
    visible.value = true

    // 立即设置 loading 状态，让用户看到加载反馈
    loading.value = true
    error.value = ''

    try {
      // 根据 props.modelValue 决定如何加载数据
      if (props.modelValue) {
        // 基于确认过的值初始化 - 只有在浮层打开后才调用
        await initializeFromValue(props.modelValue)

        // 初始化完成后，基于现有选中状态加载子集数据
        await loadChildrenBasedOnSelection()
      } else {
        // 没有确认过的值，加载初始数据
        await initializeData()
      }
    } catch (err) {
      error.value = '加载分类数据失败'
    } finally {
      // 确保在所有情况下都清除 loading 状态
      loading.value = false
    }
  }

  // 面包屑导航
  const breadcrumbs = computed(() => {
    const crumbs: { level: number; name: string; active: boolean }[] = []

    levelsData.value.forEach((levelData, index) => {
      if (levelData.selectedId) {
        const category = levelData.categories.find(cat => cat.id === levelData.selectedId)
        if (category) {
          crumbs.push({
            level: index,
            name: category.name || '',
            active: index === currentLevel.value
          })
        }
      }
    })

    return crumbs
  })

  return {
    loading,
    error,
    visible,
    displayText,
    currentValue,
    currentLevel,
    isMultipleMode,
    gridColumns,
    breadcrumbs,
    handleCategoryClick,
    handleConfirm,
    handleCancel,
    showPicker,
    retryLoad,
    goBack,
    levelsData,
    maxLevels,
    isLastLevelByNumber
  }
}
