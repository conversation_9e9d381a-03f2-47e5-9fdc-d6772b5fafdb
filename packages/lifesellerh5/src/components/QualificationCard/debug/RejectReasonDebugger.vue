<template>
  <div v-if="showDebugger" class="reject-reason-debugger">
    <div class="debug-header">
      <h3>驳回原因调试器</h3>
      <button @click="toggleDebugger" class="toggle-btn">{{ showDetails ? '收起' : '展开' }}</button>
    </div>
    
    <div v-if="showDetails" class="debug-content">
      <!-- 基础状态检查 -->
      <div class="debug-section">
        <h4>🔍 基础状态检查</h4>
        <div class="debug-item">
          <span class="label">编辑状态 (isEditing):</span>
          <span :class="['value', isEditing ? 'success' : 'error']">{{ isEditing }}</span>
        </div>
        <div class="debug-item">
          <span class="label">ShopApplyStore ID:</span>
          <span :class="['value', shopApplyId ? 'success' : 'error']">{{ shopApplyId || '未设置' }}</span>
        </div>
        <div class="debug-item">
          <span class="label">驳回数据总数:</span>
          <span :class="['value', rejectDetailList.length > 0 ? 'success' : 'error']">{{ rejectDetailList.length }}</span>
        </div>
        <div class="debug-item">
          <span class="label">QUALIFICATION模块数据:</span>
          <span :class="['value', qualificationRejectCount > 0 ? 'success' : 'error']">{{ qualificationRejectCount }}</span>
        </div>
      </div>

      <!-- 原始数据检查 -->
      <div class="debug-section">
        <h4>📊 原始数据检查</h4>
        <div class="debug-item">
          <span class="label">原始资质数据:</span>
          <span :class="['value', hasOriginalData ? 'success' : 'error']">
            {{ hasOriginalData ? '有数据' : '无数据' }}
          </span>
        </div>
        <div v-if="hasOriginalData" class="debug-detail">
          <pre>{{ JSON.stringify(originalQualificationData, null, 2) }}</pre>
        </div>
      </div>

      <!-- 当前资质检查 -->
      <div v-if="qualificationCode" class="debug-section">
        <h4>🎯 当前资质检查 ({{ qualificationCode }})</h4>
        <div class="debug-item">
          <span class="label">资质名称:</span>
          <span class="value">{{ qualificationName || '未设置' }}</span>
        </div>
        <div class="debug-item">
          <span class="label">当前图片数量:</span>
          <span class="value">{{ currentImages.length }}</span>
        </div>
        <div class="debug-item">
          <span class="label">原始资质名称:</span>
          <span class="value">{{ originalQualificationName || '未设置' }}</span>
        </div>
        <div class="debug-item">
          <span class="label">原始图片数量:</span>
          <span class="value">{{ originalImages.length }}</span>
        </div>
      </div>

      <!-- fieldCode生成检查 -->
      <div v-if="qualificationCode" class="debug-section">
        <h4>🔗 FieldCode生成检查</h4>
        <div class="debug-item">
          <span class="label">资质类型 fieldCode:</span>
          <span class="value code">{{ qualificationFieldCode }}</span>
        </div>
        <div class="debug-item">
          <span class="label">图片 fieldCode:</span>
          <span class="value code">{{ imageFieldCode }}</span>
        </div>
      </div>

      <!-- 匹配结果检查 -->
      <div v-if="qualificationCode" class="debug-section">
        <h4>✅ 匹配结果检查</h4>
        <div class="debug-item">
          <span class="label">资质类型匹配结果:</span>
          <span :class="['value', qualificationMatchResult ? 'success' : 'error']">
            {{ qualificationMatchResult || '无匹配' }}
          </span>
        </div>
        <div class="debug-item">
          <span class="label">图片匹配结果:</span>
          <span :class="['value', imageMatchResult ? 'success' : 'error']">
            {{ imageMatchResult || '无匹配' }}
          </span>
        </div>
      </div>

      <!-- RejectReasonDisplay状态检查 -->
      <div v-if="qualificationCode" class="debug-section">
        <h4>🎭 RejectReasonDisplay状态</h4>
        <div class="debug-item">
          <span class="label">资质类型显示条件:</span>
          <span :class="['value', shouldShowQualificationReason ? 'success' : 'error']">
            {{ shouldShowQualificationReason ? '满足' : '不满足' }}
          </span>
        </div>
        <div class="debug-item">
          <span class="label">图片显示条件:</span>
          <span :class="['value', shouldShowImageReason ? 'success' : 'error']">
            {{ shouldShowImageReason ? '满足' : '不满足' }}
          </span>
        </div>
      </div>

      <!-- 驳回数据详情 -->
      <div class="debug-section">
        <h4>📋 驳回数据详情</h4>
        <div v-for="(item, index) in qualificationRejectDetails" :key="index" class="reject-item">
          <div class="reject-header">
            <strong>{{ item.fieldName }}</strong>
            <span class="module-code">{{ item.moduleCode }}</span>
          </div>
          <div class="reject-field-code">{{ item.fieldCode }}</div>
          <div class="reject-content">{{ item.rejectContent }}</div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="debug-actions">
        <button @click="refreshData" class="action-btn">刷新数据</button>
        <button @click="testMatching" class="action-btn">测试匹配</button>
        <button @click="forceShow" class="action-btn">强制显示</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useStore } from 'vuex'
import { useRejectReasonQuery } from '../../RejectReasonDisplay/composables/useRejectReason'
import { useQualificationRejectReason } from '../composables/useQualificationRejectReason'

interface Props {
  qualificationCode?: string
  qualificationName?: string
  currentImages?: any[]
  enabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enabled: true,
  currentImages: () => []
})

const store = useStore()
const rejectReasonQuery = useRejectReasonQuery()
const { generateFieldCodeForDisplay } = useQualificationRejectReason()

const showDebugger = ref(props.enabled && process.env.NODE_ENV === 'development')
const showDetails = ref(false)

// 基础状态
const isEditing = computed(() => !!store.state.ShopApplyStore?.shopApplyState?.id)
const shopApplyId = computed(() => store.state.ShopApplyStore?.shopApplyState?.id)
const rejectDetailList = computed(() => store.getters['rejectReason/rejectDetailList'] || [])
const qualificationRejectCount = computed(() => 
  rejectDetailList.value.filter(item => item.moduleCode === 'QUALIFICATION').length
)

// 原始数据
const originalQualificationData = computed(() => 
  store.state.rejectReason?.rejectReasonState?.QualificationOriginalFormData || {}
)
const hasOriginalData = computed(() => Object.keys(originalQualificationData.value).length > 0)

// 当前资质数据
const originalQualificationName = computed(() => {
  if (!props.qualificationCode) return ''
  return originalQualificationData.value[props.qualificationCode]?.qualificationName || ''
})

const originalImages = computed(() => {
  if (!props.qualificationCode) return []
  return originalQualificationData.value[props.qualificationCode]?.mediaInfoList || []
})

// fieldCode生成
const qualificationFieldCode = computed(() => {
  if (!props.qualificationCode || !props.qualificationName) return ''
  return generateFieldCodeForDisplay(props.qualificationCode, 'qualification', props.qualificationName)
})

const imageFieldCode = computed(() => {
  if (!props.qualificationCode) return ''
  return generateFieldCodeForDisplay(props.qualificationCode, 'image')
})

// 匹配结果
const qualificationMatchResult = computed(() => {
  if (!qualificationFieldCode.value) return ''
  return rejectReasonQuery.getRejectContentByQualificationFieldCode(qualificationFieldCode.value)
})

const imageMatchResult = computed(() => {
  if (!imageFieldCode.value) return ''
  return rejectReasonQuery.getRejectContentByQualificationFieldCode(imageFieldCode.value)
})

// 显示条件检查
const shouldShowQualificationReason = computed(() => {
  if (!qualificationMatchResult.value) return false
  if (!originalQualificationName.value) return false
  return originalQualificationName.value === props.qualificationName
})

const shouldShowImageReason = computed(() => {
  if (!imageMatchResult.value) return false
  if (!originalImages.value.length) return false
  return JSON.stringify(originalImages.value) === JSON.stringify(props.currentImages)
})

// 驳回数据详情
const qualificationRejectDetails = computed(() => 
  rejectDetailList.value.filter(item => 
    item.moduleCode === 'QUALIFICATION' && 
    item.fieldCode?.includes('merchantCategoryDraft.categoryQualificationMap')
  )
)

const toggleDebugger = () => {
  showDetails.value = !showDetails.value
}

const refreshData = () => {
  console.log('🔄 刷新驳回原因数据...')
  // 这里可以添加刷新逻辑
}

const testMatching = () => {
  console.log('🧪 测试匹配逻辑...')
  console.log('资质类型匹配:', {
    fieldCode: qualificationFieldCode.value,
    result: qualificationMatchResult.value
  })
  console.log('图片匹配:', {
    fieldCode: imageFieldCode.value,
    result: imageMatchResult.value
  })
}

const forceShow = () => {
  console.log('🚀 强制显示驳回原因...')
  // 这里可以添加强制显示逻辑
}

// 监听变化
watch([isEditing, rejectDetailList, originalQualificationData], () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('📊 驳回原因调试数据更新:', {
      isEditing: isEditing.value,
      rejectCount: rejectDetailList.value.length,
      hasOriginalData: hasOriginalData.value
    })
  }
}, { immediate: true })
</script>

<style lang="stylus" scoped>
.reject-reason-debugger
  position fixed
  top 10px
  right 10px
  width 400px
  max-height 80vh
  overflow-y auto
  background white
  border 1px solid #ddd
  border-radius 8px
  box-shadow 0 4px 12px rgba(0,0,0,0.1)
  z-index 9999
  font-size 12px

.debug-header
  display flex
  justify-content space-between
  align-items center
  padding 12px 16px
  background #f5f5f5
  border-bottom 1px solid #ddd
  
  h3
    margin 0
    font-size 14px
    color #333

.toggle-btn, .action-btn
  padding 4px 8px
  border 1px solid #ddd
  border-radius 4px
  background white
  cursor pointer
  font-size 12px
  
  &:hover
    background #f0f0f0

.debug-content
  padding 16px

.debug-section
  margin-bottom 16px
  
  h4
    margin 0 0 8px 0
    font-size 13px
    color #666

.debug-item
  display flex
  justify-content space-between
  margin-bottom 4px
  
  .label
    font-weight 500
    color #333
    
  .value
    font-family monospace
    
    &.success
      color #52c41a
      
    &.error
      color #ff4d4f
      
    &.code
      background #f5f5f5
      padding 2px 4px
      border-radius 2px

.debug-detail
  margin-top 8px
  
  pre
    background #f5f5f5
    padding 8px
    border-radius 4px
    font-size 10px
    max-height 100px
    overflow auto

.reject-item
  border 1px solid #eee
  border-radius 4px
  padding 8px
  margin-bottom 8px
  
  .reject-header
    display flex
    justify-content space-between
    margin-bottom 4px
    
    .module-code
      background #e6f7ff
      color #1890ff
      padding 2px 6px
      border-radius 2px
      font-size 10px
      
  .reject-field-code
    font-family monospace
    font-size 10px
    color #666
    margin-bottom 4px
    
  .reject-content
    color #333
    line-height 1.4

.debug-actions
  display flex
  gap 8px
  margin-top 16px
  
  .action-btn
    flex 1
</style>
