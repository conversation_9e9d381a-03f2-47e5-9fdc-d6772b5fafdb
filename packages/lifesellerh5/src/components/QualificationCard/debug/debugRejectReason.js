/**
 * 驳回原因调试脚本
 * 在浏览器控制台中运行此脚本来排查驳回原因不显示的问题
 */

window.debugRejectReason = function() {
  console.log('🔍 开始调试驳回原因显示问题...\n');
  
  // 1. 检查 Vue 应用实例
  const app = document.querySelector('#app').__vue_app__;
  if (!app) {
    console.error('❌ 无法找到 Vue 应用实例');
    return;
  }
  
  // 2. 检查 store 状态
  const store = app.config.globalProperties.$store;
  if (!store) {
    console.error('❌ 无法找到 Vuex store');
    return;
  }
  
  console.log('✅ Vue 应用和 Store 检查通过\n');
  
  // 3. 检查编辑状态
  const shopApplyState = store.state.ShopApplyStore?.shopApplyState;
  const isEditing = !!shopApplyState?.id;
  console.log('📊 编辑状态检查:');
  console.log('  - ShopApplyStore ID:', shopApplyState?.id || '未设置');
  console.log('  - 是否在编辑状态:', isEditing);
  
  if (!isEditing) {
    console.warn('⚠️  当前不在编辑状态，驳回原因不会显示');
    console.log('💡 解决方案: 确保调用了 getApplyQuery API 并设置了 id');
  }
  console.log('');
  
  // 4. 检查驳回数据
  const rejectDetailList = store.getters['rejectReason/rejectDetailList'] || [];
  const qualificationRejects = rejectDetailList.filter(item => 
    item.moduleCode === 'QUALIFICATION' && 
    item.fieldCode?.includes('merchantCategoryDraft.categoryQualificationMap')
  );
  
  console.log('📋 驳回数据检查:');
  console.log('  - 总驳回记录数:', rejectDetailList.length);
  console.log('  - QUALIFICATION 模块记录数:', qualificationRejects.length);
  
  if (rejectDetailList.length === 0) {
    console.warn('⚠️  没有驳回数据');
    console.log('💡 解决方案: 调用 store.dispatch("rejectReason/getRejectReasonList", { applyId: "your_id" })');
  } else if (qualificationRejects.length === 0) {
    console.warn('⚠️  没有 QUALIFICATION 模块的驳回数据');
    console.log('💡 检查驳回数据是否包含正确的 moduleCode 和 fieldCode');
  }
  
  // 显示驳回数据详情
  if (qualificationRejects.length > 0) {
    console.log('  - QUALIFICATION 驳回详情:');
    qualificationRejects.forEach((item, index) => {
      console.log(`    ${index + 1}. ${item.fieldName}`);
      console.log(`       fieldCode: ${item.fieldCode}`);
      console.log(`       内容: ${item.rejectContent?.substring(0, 50)}...`);
    });
  }
  console.log('');
  
  // 5. 检查原始数据
  const originalQualificationData = store.state.rejectReason?.rejectReasonState?.QualificationOriginalFormData || {};
  const hasOriginalData = Object.keys(originalQualificationData).length > 0;
  
  console.log('💾 原始数据检查:');
  console.log('  - 是否有原始资质数据:', hasOriginalData);
  
  if (!hasOriginalData) {
    console.warn('⚠️  没有原始资质数据');
    console.log('💡 解决方案: 确保调用了 store.commit("rejectReason/SET_QUALIFICATION_ORIGINAL_DATA", data)');
  } else {
    console.log('  - 原始数据包含的资质:');
    Object.keys(originalQualificationData).forEach(code => {
      const data = originalQualificationData[code];
      console.log(`    ${code}: ${data.qualificationName || '未命名'} (图片: ${data.mediaInfoList?.length || 0})`);
    });
  }
  console.log('');
  
  // 6. 模拟 RejectReasonDisplay 的显示条件检查
  console.log('🎭 RejectReasonDisplay 显示条件模拟:');
  
  // 模拟一个资质的检查
  if (hasOriginalData && qualificationRejects.length > 0) {
    const firstQualificationCode = Object.keys(originalQualificationData)[0];
    const firstQualificationData = originalQualificationData[firstQualificationCode];
    
    if (firstQualificationCode && firstQualificationData) {
      console.log(`  检查资质: ${firstQualificationCode}`);
      
      // 生成 fieldCode
      const qualificationFieldCode = `merchantCategoryDraft.categoryQualificationMap*.qualificationList*${firstQualificationData.qualificationName}`;
      const imageFieldCode = `merchantCategoryDraft.categoryQualificationMap*.qualificationList*${firstQualificationCode}.fileAttachmentList`;
      
      console.log(`  - 资质类型 fieldCode: ${qualificationFieldCode}`);
      console.log(`  - 图片 fieldCode: ${imageFieldCode}`);
      
      // 检查匹配
      const qualificationMatch = qualificationRejects.find(item => {
        const itemParts = item.fieldCode?.match(/qualificationList\*([^.]+)(?:\.(.+))?$/);
        const queryParts = qualificationFieldCode.match(/qualificationList\*([^.]+)(?:\.(.+))?$/);
        return itemParts?.[1] === queryParts?.[1] && !itemParts?.[2] && !queryParts?.[2];
      });
      
      const imageMatch = qualificationRejects.find(item => {
        const itemParts = item.fieldCode?.match(/qualificationList\*([^.]+)(?:\.(.+))?$/);
        const queryParts = imageFieldCode.match(/qualificationList\*([^.]+)(?:\.(.+))?$/);
        return itemParts?.[1] === queryParts?.[1] && itemParts?.[2] === 'fileAttachmentList';
      });
      
      console.log(`  - 资质类型匹配结果: ${qualificationMatch ? '✅ 匹配' : '❌ 无匹配'}`);
      console.log(`  - 图片匹配结果: ${imageMatch ? '✅ 匹配' : '❌ 无匹配'}`);
      
      if (qualificationMatch) {
        console.log(`    匹配内容: ${qualificationMatch.rejectContent?.substring(0, 50)}...`);
      }
      if (imageMatch) {
        console.log(`    匹配内容: ${imageMatch.rejectContent?.substring(0, 50)}...`);
      }
    }
  }
  console.log('');
  
  // 7. 检查 RejectReasonDisplay 组件
  console.log('🔍 查找页面中的 RejectReasonDisplay 组件...');
  const rejectReasonElements = document.querySelectorAll('[class*="reject-reason"]');
  console.log(`  - 找到 ${rejectReasonElements.length} 个可能的驳回原因元素`);
  
  if (rejectReasonElements.length === 0) {
    console.warn('⚠️  页面中没有找到驳回原因相关元素');
    console.log('💡 检查 RejectReasonDisplay 组件是否正确渲染');
  }
  
  // 8. 总结和建议
  console.log('📝 问题排查总结:');
  const issues = [];
  const solutions = [];
  
  if (!isEditing) {
    issues.push('不在编辑状态');
    solutions.push('调用 getApplyQuery API 设置 ShopApplyStore.id');
  }
  
  if (rejectDetailList.length === 0) {
    issues.push('没有驳回数据');
    solutions.push('调用 getRejectReasonList action 获取驳回数据');
  }
  
  if (!hasOriginalData) {
    issues.push('没有原始数据');
    solutions.push('设置 QualificationOriginalFormData');
  }
  
  if (qualificationRejects.length === 0 && rejectDetailList.length > 0) {
    issues.push('没有 QUALIFICATION 模块的驳回数据');
    solutions.push('检查驳回数据的 moduleCode 和 fieldCode 格式');
  }
  
  if (issues.length === 0) {
    console.log('✅ 所有基础条件都满足，问题可能在组件内部逻辑');
    console.log('💡 建议: 检查 RejectReasonDisplay 组件的 shouldShowRejectReason 计算属性');
  } else {
    console.log('❌ 发现以下问题:');
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
    console.log('');
    console.log('💡 建议的解决方案:');
    solutions.forEach((solution, index) => {
      console.log(`  ${index + 1}. ${solution}`);
    });
  }
  
  console.log('\n🔧 快速修复命令:');
  console.log('// 1. 设置编辑状态');
  console.log('$store.commit("ShopApplyStore/UPDATE_SHOP_APPLY_STATE", { id: 123 })');
  console.log('');
  console.log('// 2. 设置测试驳回数据');
  console.log('$store.commit("rejectReason/SET_DEPOSIT_DETAIL_LIST", [');
  console.log('  {');
  console.log('    "fieldCode": "merchantCategoryDraft.categoryQualificationMap*test.qualificationList*测试资质",');
  console.log('    "rejectContent": "测试驳回原因",');
  console.log('    "fieldName": "测试资质",');
  console.log('    "moduleCode": "QUALIFICATION"');
  console.log('  }');
  console.log('])');
  console.log('');
  console.log('// 3. 设置原始数据');
  console.log('$store.commit("rejectReason/SET_QUALIFICATION_ORIGINAL_DATA", {');
  console.log('  "test_qualification_code": {');
  console.log('    "qualificationName": "测试资质",');
  console.log('    "mediaInfoList": []');
  console.log('  }');
  console.log('})');
  
  return {
    isEditing,
    rejectDetailList,
    qualificationRejects,
    originalQualificationData,
    hasOriginalData,
    issues,
    solutions
  };
};

// 自动运行调试
if (typeof window !== 'undefined') {
  console.log('🚀 驳回原因调试脚本已加载');
  console.log('💡 在控制台运行 debugRejectReason() 开始调试');
}
