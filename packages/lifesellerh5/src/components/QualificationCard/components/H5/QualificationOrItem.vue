<template>
  <div class="qualification-or-item-wrapper">
    <!-- 每个资质类型独立的卡片 -->
    <div
      v-for="item in elements"
      :key="item.qualificationConfig?.qualificationCode"
      class="qualification-card"
    >
      <!-- 资质类型显示区域 -->
      <div class="qualification-type-section">
        <div class="qualification-type-header">
          <span class="qualification-type-label">资质类型</span>
          <div class="qualification-type-display">
            <span class="qualification-text">{{ item.qualificationConfig?.qualificationName || '-' }}</span>
          </div>
        </div>

        <!-- 资质类型拒绝原因 -->
        <RejectReasonDisplay
          v-if="item.qualificationConfig?.qualificationCode && item.qualificationConfig?.qualificationName"
          :field-code="generateFieldCodeForDisplay(item.qualificationConfig.qualificationCode, 'qualification', item.qualificationConfig.qualificationName)"
          :current-value="item.qualificationConfig?.qualificationName"
          :original-value="getOriginalQualificationName(item.qualificationConfig?.qualificationCode)"
          match-strategy="fuzzy"
        />
      </div>

      <!-- 证明材料区域 -->
      <div class="qualification-materials-section">
        <!-- 资质图片上传组件 -->
        <QualificationImage
          :model-value="getImages(item.qualificationConfig?.qualificationCode)"
          :read-only="readonly"
          :disabled="disabled"
          :max-count="uploadConfig.maxCount"
          :max-size="uploadConfig.maxSize"
          @change="(newImages) => handleImageListUpdate(newImages, item.qualificationConfig)"
        />

        <!-- 图片上传拒绝原因 -->
        <RejectReasonDisplay
          v-if="item.qualificationConfig?.qualificationCode"
          :field-code="generateFieldCodeForDisplay(item.qualificationConfig.qualificationCode, 'image')"
          :current-value="getImages(item.qualificationConfig?.qualificationCode)"
          :original-value="getOriginalImages(item.qualificationConfig?.qualificationCode)"
          match-strategy="qualification"
        />
      </div>

      <!-- 有效期区域 -->
      <div class="validity-section">
        <ValidityTimePicker
          :model-value="getValidity(item.qualificationConfig?.qualificationCode)"
          :read-only="readonly"
          :disabled="disabled"
          @update:model-value="(newValidity) => handleValidityChange(newValidity, item.qualificationConfig)"
        />

        <!-- 有效期验证错误提示 -->
        <div v-if="getValidityError(item.qualificationConfig?.qualificationCode)" class="error-message">
          {{ getValidityError(item.qualificationConfig?.qualificationCode) }}
        </div>

        <!-- 图片上传错误提示 -->
        <div v-if="getImageError(item.qualificationConfig?.qualificationCode)" class="error-message">
          {{ getImageError(item.qualificationConfig?.qualificationCode) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { useStore } from 'vuex'
  import { IQualificationElement, IQualificationConfig } from '@edith/edith_get_query_qualification_config'

  // 导入组件
  import QualificationImage from '../../../QualificationImage/components/H5/BusinessLicenseImage.vue'
  import ValidityTimePicker from '../../../ValidityTimePicker/index.vue'
  import RejectReasonDisplay from '../../../RejectReasonDisplay/index.vue'

  // 导入类型
  import {
    QualificationItem,
    LicenseValidItem,
    Period
  } from '../../core/type'
  import type { QualificationImageItem } from '../../../QualificationImage/types'

  // 导入composable
  import { useQualificationRejectReason } from '../../composables/useQualificationRejectReason'

  // Props 定义
  const props = withDefaults(defineProps<{
    modelValue: Record<string, QualificationItem>
    disabled?: boolean
    readonly?: boolean
    name?: string
    elements: IQualificationElement[]
    validation?: {
      getFieldError:(qualificationCode: string, field: string) => string
      watchDataChange:(qualificationCode: string, field: string) => void
    }
  }>(), {
    name: 'qualificationMap',
    disabled: false,
    readonly: false,
    elements: () => []
  })

  // Events 定义
  const emit = defineEmits(['update:modelValue', 'change'])

  // 使用store获取原始数据
  const store = useStore()

  // 使用拒绝原因composable
  const { generateFieldCodeForDisplay } = useQualificationRejectReason()

  // 响应式状态
  const imageErrors = ref<Record<string, string>>({})
  const validityErrors = ref<Record<string, string>>({})

  // 上传配置
  const uploadConfig = {
    maxCount: 5,
    maxSize: 5 // 5MB
  }

  // 获取原始资质数据（用于拒绝原因比较）
  const originalQualificationData = computed(() => store.state.rejectReason?.rejectReasonState?.QualificationOriginalFormData || {})

  // 获取原始资质名称
  const getOriginalQualificationName = (qualificationCode?: string): string => {
    if (!qualificationCode) return ''
    return originalQualificationData.value[qualificationCode]?.qualificationName || ''
  }

  // 获取原始图片列表
  const getOriginalImages = (qualificationCode?: string): QualificationImageItem[] => {
    if (!qualificationCode) return []
    return originalQualificationData.value[qualificationCode]?.mediaInfoList || []
  }

  // 计算合并后的完整数据 - 基于elements结构和modelValue数据
  const mergedValue = computed(() => {
    const result: Record<string, QualificationItem> = {}

    // 遍历elements，创建完整的数据结构
    props.elements?.forEach(element => {
      const qualificationCode = element.qualificationConfig?.qualificationCode
      if (qualificationCode) {
        // 从modelValue获取已有的用户数据
        const existingData = props.modelValue?.[qualificationCode]

        // 合并配置和用户数据
        result[qualificationCode] = {
          qualificationCode,
          qualificationName: element.qualificationConfig?.qualificationName || '',
          mediaInfoList: existingData?.mediaInfoList || [],
          qualificationValidity: existingData?.qualificationValidity || {
            qualValidityPeriod: null,
            startTime: 0,
            endTime: 0
          }
        }
      }
    })

    return result
  })

  // 更新modelValue的方法
  const updateModelValue = (newValue: Record<string, QualificationItem>) => {
    emit('update:modelValue', newValue)
    emit('change', newValue)
  }

  // 初始化时，如果elements存在但modelValue为空，则初始化完整结构
  watch(
    [() => props.elements, () => props.modelValue],
    ([elements, modelValue]) => {
      if (elements && elements.length > 0) {
        const merged = mergedValue.value
        const hasNewStructure = Object.keys(merged).some(key => !modelValue?.[key])

        // 如果有新的结构（新的qualificationCode），则初始化并向外传递
        if (hasNewStructure) {
          // 🔧 修复：合并现有数据，而不是覆盖
          const newValue = {
            ...(modelValue || {}), // 保留现有的所有数据
            ...merged // 只添加新的结构
          }
          updateModelValue(newValue)
        }
      }
    },
    { immediate: true }
  )

  // 更新资质数据的通用方法
  const updateQualificationData = (typeCode: string, updates: Partial<QualificationItem>) => {
    if (!typeCode) return

    // 🔧 修复：使用完整的 props.modelValue，而不是 mergedValue
    const currentValue = props.modelValue || {}
    const oldData = currentValue[typeCode] || mergedValue.value[typeCode]

    // 更新数据
    const newData = {
      ...oldData,
      ...updates
    }

    // 检查是否真的有数据变化
    const hasRealChange = JSON.stringify(oldData?.mediaInfoList || []) !== JSON.stringify(newData.mediaInfoList || [])
      || JSON.stringify(oldData?.qualificationValidity || {}) !== JSON.stringify(newData.qualificationValidity || {})

    // 只有在真实数据变化时才触发更新
    if (hasRealChange) {
      // 🔧 修复：保留完整的 modelValue 数据，只更新当前资质项
      const newValue = {
        ...currentValue, // 保留所有其他组件的数据
        [typeCode]: newData
      }
      updateModelValue(newValue)
    }
  }

  // 获取指定资质类型的图片列表
  const getImages = (typeCode?: string): QualificationImageItem[] => {
    if (!typeCode) return []
    const item = mergedValue.value[typeCode]
    // 如果数据为 null 或 undefined，返回空数组
    if (!item || item === null) return []
    return item.mediaInfoList || []
  }

  // 获取指定资质类型的有效期
  const getValidity = (typeCode?: string) => {
    if (!typeCode) return null
    const item = mergedValue.value[typeCode]
    // 如果数据为 null 或 undefined，返回默认有效期
    if (!item || item === null) {
      return {
        qualValidityPeriod: null,
        startTime: 0,
        endTime: 0
      }
    }

    const validity = item.qualificationValidity
    if (!validity || validity.qualValidityPeriod === null) {
      return {
        qualValidityPeriod: null,
        startTime: 0,
        endTime: 0
      }
    }

    // 转换为 ValidityTimePickerValue 格式
    return {
      qualValidityPeriod: validity.qualValidityPeriod,
      startTime: validity.startTime,
      endTime: validity.endTime
    }
  }

  // 检查是否有图片
  const hasImages = (typeCode?: string): boolean => {
    if (!typeCode) return false
    return getImages(typeCode).length > 0
  }

  // 获取图片验证错误
  const getImageError = (typeCode?: string): string => {
    if (!typeCode) return ''
    return props.validation?.getFieldError(typeCode, 'mediaInfoList') || imageErrors.value[typeCode] || ''
  }

  // 获取有效期验证错误
  const getValidityError = (typeCode?: string): string => {
    if (!typeCode) return ''
    return props.validation?.getFieldError(typeCode, 'qualificationValidity') || validityErrors.value[typeCode] || ''
  }

  // 处理图片列表更新
  const handleImageListUpdate = (newImages: QualificationImageItem[], config?: IQualificationConfig) => {
    if (!config?.qualificationCode) return

    const typeCode = config.qualificationCode

    // 清除之前的错误
    delete imageErrors.value[typeCode]

    // QualificationImage 组件已经处理了格式转换和验证，直接使用
    updateQualificationData(typeCode, { mediaInfoList: newImages })
  }

  // 处理有效期变化
  const handleValidityChange = (newValidity: any, config?: IQualificationConfig) => {
    if (!config?.qualificationCode) return

    const typeCode = config.qualificationCode

    // 清除之前的错误
    delete validityErrors.value[typeCode]

    // 验证有效期
    if (!validateValidity(newValidity, typeCode)) {
      return
    }

    // 转换为 LicenseValidItem 格式
    const licenseValid: LicenseValidItem = newValidity ? {
      qualValidityPeriod: newValidity.qualValidityPeriod,
      startTime: newValidity.startTime || 0,
      endTime: newValidity.endTime || 0
    } : {
      qualValidityPeriod: null,
      startTime: 0,
      endTime: 0
    }

    // 更新有效期数据
    updateQualificationData(typeCode, { qualificationValidity: licenseValid })
  }

  // 验证有效期（OR逻辑：条件验证）
  const validateValidity = (validity: any, typeCode: string): boolean => {
    delete validityErrors.value[typeCode]

    if (props.readonly || props.disabled) {
      return true
    }

    // OR逻辑：如果有图片，则有效期为必填
    const hasImagesSet = hasImages(typeCode)

    if (hasImagesSet && !validity) {
      validityErrors.value[typeCode] = '上传图片后必须设置有效期'
      return false
    }

    if (validity) {
      // 验证期限有效的情况
      if (validity.qualValidityPeriod === Period.NON_PERMANENT) {
        if (!validity.endTime) {
          validityErrors.value[typeCode] = '请设置有效期结束时间'
          return false
        }

        const endTime = validity.endTime

        if (endTime <= Date.now()) {
          validityErrors.value[typeCode] = '有效期不能早于当前时间'
          return false
        }

        // 检查30天限制
        const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000)
        if (endTime <= thirtyDaysFromNow) {
          validityErrors.value[typeCode] = '有效期剩余时间应大于30天'
          return false
        }
      }
    }

    return true
  }
</script>

<style scoped lang="stylus">
.qualification-or-item-wrapper
  display flex
  flex-direction column
  gap 16px

.qualification-card
  padding 16px
  background var(--bg)
  border-radius 12px
  border 1px solid var(--border-color, #e0e0e0)
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.04)

.qualification-type-section
  margin-bottom 20px

.qualification-type-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 12px

.qualification-type-label
  font-weight 500
  font-size 16px
  color var(--title)
  white-space nowrap
  margin-right 12px

.optional-indicator
  color #52c41a
  font-size 12px
  background-color rgba(82, 196, 26, 0.1)
  padding 2px 8px
  border-radius 4px

.qualification-type-display
  display flex
  align-items center
  gap 8px

.qualification-text
  color var(--title)
  font-size 16px
  line-height 1.4
  text-align right

.qualification-materials-section
  margin-bottom 20px

.materials-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 8px

.materials-title
  font-weight 500
  font-size 16px
  color var(--title)

.conditional-required
  color #fa8c16
  font-size 12px
  background-color rgba(250, 140, 22, 0.1)
  padding 2px 8px
  border-radius 4px

.materials-tip
  font-size 12px
  color var(--Light-Labels-Description, rgba(0, 0, 0, 0.45))
  margin-bottom 16px
  line-height 1.4

.validity-header
  display flex
  align-items center
  justify-content space-between
  margin-bottom 12px

.validity-title
  font-weight 500
  font-size 16px
  color var(--title)

.error-message
  color #ff4d4f;
  font-size 12px
  line-height 17px
  font-weight 400
  word-break break-word
</style>
