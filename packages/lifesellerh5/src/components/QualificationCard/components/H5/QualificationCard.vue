<template>
  <div class="qualification-card-container">
    <component
      :is="qualificationGroup.func === Func.AND ? QualificationAndItem : QualificationOrItem"
      v-model="value"
      :readonly="componentProps?.readonly"
      :elements="qualificationGroup.qualificationElements || []"
      :validation="validation"
    />

    <!-- 全局错误提示 -->
    <div v-if="validation.hasErrors.value" class="global-error-container">
      <div
        v-for="error in getAllGlobalErrors()"
        :key="`${error.qualificationCode}-${error.field}`"
        class="error-message global-error"
      >
        {{ error.message }}
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { computed, watch } from 'vue'
  import { IQualificationGroupList } from '@edith/edith_get_query_qualification_config'
  import { QualificationItem, Func } from '../../core/type'
  import { useValidation, ValidationError } from '../../composables/useValidation'
  import QualificationOrItem from './QualificationOrItem.vue'
  import QualificationAndItem from './QualificationAndItem.vue'

  const props = withDefaults(
    defineProps<{
      modelValue: Record<string, QualificationItem>
      qualificationGroup: IQualificationGroupList
      componentProps?: {
        disabled?: boolean
        readonly?: boolean
        onLoad?:(loaded: boolean) => void
      }
      name?: string
    }>(),
    {
      name: 'qualificationMap'
    }
  )

  const emit = defineEmits(['update:modelValue', 'change'])

  // 初始化校验器
  const validation = useValidation(props.qualificationGroup.qualificationElements || [])

  const value = computed({
    get: () => props.modelValue,
    set: val => {
      emit('update:modelValue', val)
      emit('change', val)
    }
  })

  // 获取所有全局错误
  const getAllGlobalErrors = (): ValidationError[] => {
    const allErrors: ValidationError[] = []
    Object.keys(validation.errors.value).forEach(qualificationCode => {
      const errors = validation.getErrors(qualificationCode)
      Object.keys(errors).forEach(field => {
        if (field === 'global' && errors[field]) {
          allErrors.push({
            field,
            message: errors[field],
            qualificationCode
          })
        }
      })
    })
    return allErrors
  }

  // 监听数据变化，自动清除错误
  watch(
    () => props.modelValue,
    (newVal, oldVal) => {
      if (!newVal || !oldVal) return

      Object.keys(newVal).forEach(qualificationCode => {
        const newItem = newVal[qualificationCode]
        const oldItem = oldVal[qualificationCode]

        // 检查图片列表是否发生变化
        if (JSON.stringify(newItem?.mediaInfoList) !== JSON.stringify(oldItem?.mediaInfoList)) {
          validation.watchDataChange(qualificationCode, 'mediaInfoList')
        }

        // 检查有效期是否发生变化
        if (JSON.stringify(newItem?.qualificationValidity) !== JSON.stringify(oldItem?.qualificationValidity)) {
          validation.watchDataChange(qualificationCode, 'qualificationValidity')
        }
      })
    },
    { deep: true }
  )

  // 对外暴露校验方法
  defineExpose({
    // 校验所有资质
    validate: () => validation.validateAll(props.modelValue),

    // 校验指定资质
    validateQualification: (qualificationCode: string) => {
      const item = props.modelValue[qualificationCode]
      return item ? validation.validateQualification(qualificationCode, item) : { isValid: false, errors: [] }
    },

    // 手动显示错误信息
    showError: validation.showError,

    // 清除错误信息
    clearErrors: validation.clearErrors,
    clearAllErrors: validation.clearAllErrors,

    // 获取错误信息
    getErrors: validation.getErrors,
    getFieldError: validation.getFieldError,

    // 错误状态
    hasErrors: validation.hasErrors
  })

</script>

<style lang="stylus" scoped>
.qualification-card-container
  display flex
  flex-direction column

.qualification-outer
  display flex
  flex-direction column
  gap 20px
  padding-bottom 10px

.gray-card
  background-color #fafafa
  padding 20px
  border-radius 4px
  display flex
  flex-direction column
  gap: 12px

.global-error-container
  margin-top 12px

.error-message
  color #ff4d4f;
  font-size 12px
  line-height 17px
  font-weight 400
  word-break break-word

  &.global-error
    background-color #fff2f0
    border 1px solid #ffccc7
    border-radius 4px
    padding 8px 12px
    margin-top 8px
</style>
