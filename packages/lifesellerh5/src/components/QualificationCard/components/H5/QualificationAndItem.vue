<template>
  <div class="qualification-and-item-wrapper">
    <!-- 资质类型选择区域 -->
    <div class="qualification-type-section">
      <div class="qualification-type-header">
        <span class="qualification-type-label">资质类型</span>
        <div class="picker-trigger" @click="handleShowPicker">
          <span class="picker-text">{{ qualificationName || '请选择资质类型' }}</span>
          <OnixIcon v-if="!readonly" class="date-icon" icon="arrowRightRightM" />
        </div>
      </div>

      <!-- 资质类型拒绝原因 -->
      <RejectReasonDisplay
        v-if="qualificationCode && qualificationName"
        :field-code="generateFieldCodeForDisplay(qualificationCode, 'qualification', qualificationName)"
        :current-value="qualificationName"
        :original-value="getOriginalQualificationName(qualificationCode)"
        match-strategy="qualification"
      />

      <Divider direction="horizontal" :margin="['0px', '0px']" />

      <!-- 资质类型选择器 -->
      <template v-if="!readonly">
        <Picker
          :columns="[options]"
          :value="[qualificationCode]"
          :visible="pickerVisible"
          :cancel="true"
          :close="true"
          :close-type="SheetsType.SheetsActionType.text"
          :cancel-type="SheetsType.SheetsActionType.text"
          :cancel-text="'取消'"
          :confirm-text="'确定'"
          :label="'选择资质类型'"
          @confirm="handlePickerConfirm"
          @cancel="handlePickerCancel"
        />
      </template>
      <div v-else class="qualification-type-display">
        <div class="qualification-text">{{ qualificationName || '未选择' }}</div>
      </div>
    </div>

    <!-- 选中资质类型后显示的内容 -->
    <template v-if="qualificationCode">
      <!-- 证明材料区域 -->
      <div>
        <!-- 资质图片上传组件 -->
        <QualificationImage
          v-model="currentImages"
          :read-only="readonly"
          :disabled="disabled"
          :max-count="uploadConfig.maxCount"
          :max-size="uploadConfig.maxSize"
          @change="handleImageListUpdate"
        />

        <!-- 图片上传拒绝原因 -->
        <RejectReasonDisplay
          :field-code="generateFieldCodeForDisplay(qualificationCode, 'image')"
          :current-value="currentImages"
          :original-value="getOriginalImages(qualificationCode)"
          match-strategy="qualification"
        />
      </div>

      <Divider direction="horizontal" :margin="['0px', '0px']" />

      <!-- 有效期区域 -->
      <div class="validity-section">
        <ValidityTimePicker
          :model-value="currentValidity"
          :read-only="readonly"
          :disabled="disabled"
          @update:model-value="handleValidityChange"
        />

        <!-- 有效期验证错误提示 -->
        <div v-if="getValidityError()" class="error-message">
          {{ getValidityError() }}
        </div>

        <!-- 图片上传错误提示 -->
        <div v-if="getImageError()" class="error-message">
          {{ getImageError() }}
        </div>
      </div>
    </template>

    <!-- 全局错误提示 -->
    <div v-if="globalError" class="error-message global-error">
      {{ globalError }}
    </div>
  </div>
</template>

<script lang="tsx" setup>
  import { ref, watch, computed } from 'vue'
  import { useStore } from 'vuex'
  import { Picker, SheetsType, Divider } from '@xhs/reds-h5-next'
  import { IQualificationElement } from '@edith/edith_get_query_qualification_config'
  import OnixIcon from '@xhs/onix-icon'

  // 导入组件
  import QualificationImage from '../../../QualificationImage/components/H5/BusinessLicenseImage.vue'
  import ValidityTimePicker from '../../../ValidityTimePicker/index.vue'
  import RejectReasonDisplay from '../../../RejectReasonDisplay/index.vue'

  // 导入类型和组合式函数
  import {
    QualificationItem,
    LicenseValidItem,
    ImageItem,
    Period
  } from '../../core/type'

  // 导入composable
  import { useQualificationRejectReason } from '../../composables/useQualificationRejectReason'

  // Props 定义
  const props = withDefaults(defineProps<{
    modelValue: Record<string, QualificationItem>
    disabled?: boolean
    readonly?: boolean
    name?: string
    elements: IQualificationElement[]
    validation?: {
      getFieldError:(qualificationCode: string, field: string) => string
      watchDataChange:(qualificationCode: string, field: string) => void
    }
  }>(), {
    name: 'qualificationMap',
    disabled: false,
    readonly: false,
    elements: () => []
  })

  // Events 定义
  const emit = defineEmits(['update:modelValue'])

  // 使用store获取原始数据
  const store = useStore()

  // 使用拒绝原因composable
  const { generateFieldCodeForDisplay } = useQualificationRejectReason()

  // 响应式状态
  const value = ref(props.modelValue || {})
  const imageValidationError = ref('')
  const validityValidationError = ref('')
  const globalError = ref('')
  const pickerVisible = ref(false)

  // 获取原始资质数据（用于拒绝原因比较）
  const originalQualificationData = computed(() => store.state.rejectReason?.rejectReasonState?.QualificationOriginalFormData || {})

  // 获取原始资质名称
  const getOriginalQualificationName = (qualificationCode?: string): string => {
    if (!qualificationCode) return ''
    return originalQualificationData.value[qualificationCode]?.qualificationName || ''
  }

  // 获取原始图片列表
  const getOriginalImages = (qualificationCode?: string): ImageItem[] => {
    if (!qualificationCode) return []
    return originalQualificationData.value[qualificationCode]?.mediaInfoList || []
  }

  // 获取图片验证错误
  const getImageError = (): string => {
    if (!qualificationCode.value) return ''
    return props.validation?.getFieldError(qualificationCode.value, 'mediaInfoList') || imageValidationError.value || ''
  }

  // 获取有效期验证错误
  const getValidityError = (): string => {
    if (!qualificationCode.value) return ''
    return props.validation?.getFieldError(qualificationCode.value, 'qualificationValidity') || validityValidationError.value || ''
  }

  // 上传配置
  const uploadConfig = {
    maxCount: 5,
    maxSize: 5 // 5MB
  }

  // 触发变更事件
  const change = () => {
    emit('update:modelValue', value.value)
  }

  // 初始化默认选择
  const initializeDefaultSelection = () => {
    // 检查是否已有数据
    const hasExistingData = Object.keys(value.value).length > 0

    if (!hasExistingData && props.elements && props.elements.length > 0) {
      // 获取第一个可用的资质类型
      const firstElement = props.elements[0]
      const firstCode = firstElement?.qualificationConfig?.qualificationCode
      const firstName = firstElement?.qualificationConfig?.qualificationName

      if (firstCode) {
        // 自动创建第一个资质类型的数据
        value.value = {
          [firstCode]: {
            qualificationCode: firstCode,
            qualificationName: firstName || '',
            mediaInfoList: [],
            qualificationValidity: {
              qualValidityPeriod: null,
              startTime: 0,
              endTime: 0
            }
          }
        }

        // 触发变更事件
        change()
      }
    }
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    val => {
      value.value = val || {}
      // 初始化时如果没有数据，自动选择第一个选项
      initializeDefaultSelection()
    },
    { immediate: true }
  )

  // 监听选项变化
  watch(
    () => props.elements,
    () => {
      // 当选项更新时，检查是否需要初始化
      initializeDefaultSelection()
    },
    { immediate: true }
  )

  // 计算属性 - 资质类型选项
  const options = computed(() => (props.elements || []).map(item => ({
    label: item.qualificationConfig?.qualificationName,
    value: item.qualificationConfig?.qualificationCode
  })))

  // 计算属性 - 当前选中的资质类型（AND逻辑：单选）
  const qualificationCode = computed(() => {
    const keys = (props.elements || []).map(t => t.qualificationConfig?.qualificationCode).filter(Boolean) as string[]
    // 首先查找已存在的资质类型
    const existingKey = keys.find(key => key in value.value)
    // 如果没有已存在的，返回第一个可用的，如果没有可用的返回空字符串
    return existingKey || keys[0] || ''
  })

  // 计算属性 - 当前资质配置
  const qualificationConfig = computed(() => {
    const element = (props.elements || []).find(e => e.qualificationConfig?.qualificationCode === qualificationCode.value)
    return element?.qualificationConfig || {}
  })

  // 计算属性 - 资质名称
  const qualificationName = computed(() => qualificationConfig.value?.qualificationName || '')

  // 计算属性 - 当前图片列表
  const currentImages = computed({
    get: () => {
      if (!qualificationCode.value) return []
      return value.value[qualificationCode.value]?.mediaInfoList || []
    },
    set: (newImages: ImageItem[]) => {
      if (!qualificationCode.value) return
      updateQualificationData(qualificationCode.value, { mediaInfoList: newImages })
    }
  })

  // 计算属性 - 当前有效期
  const currentValidity = computed({
    get: () => {
      if (!qualificationCode.value) return null
      const validity = value.value[qualificationCode.value]?.qualificationValidity
      if (!validity || validity.qualValidityPeriod === null) return null

      // 转换为 ValidityTimePickerValue 格式
      return {
        qualValidityPeriod: validity.qualValidityPeriod,
        startTime: validity.startTime,
        endTime: validity.endTime
      }
    },
    set: newValidity => {
      if (!qualificationCode.value) return

      // 转换为 LicenseValidItem 格式
      const licenseValid: LicenseValidItem = newValidity ? {
        qualValidityPeriod: newValidity.qualValidityPeriod,
        startTime: newValidity.startTime || 0,
        endTime: newValidity.endTime || 0
      } : {
        qualValidityPeriod: null,
        startTime: 0,
        endTime: 0
      }

      updateQualificationData(qualificationCode.value, { qualificationValidity: licenseValid })
    }
  })

  // 更新资质数据的通用方法
  const updateQualificationData = (typeCode: string, updates: Partial<QualificationItem>) => {
    if (!typeCode) return

    // 查找对应的配置信息
    const config = (props.elements || []).find(e => e.qualificationConfig?.qualificationCode === typeCode)?.qualificationConfig

    // 确保资质项存在
    if (!value.value[typeCode]) {
      value.value[typeCode] = {
        qualificationCode: typeCode,
        qualificationName: config?.qualificationName || '',
        mediaInfoList: [],
        qualificationValidity: {
          qualValidityPeriod: null,
          startTime: 0,
          endTime: 0
        }
      }
    }

    // 安全更新数据
    value.value[typeCode] = {
      ...value.value[typeCode],
      ...updates
    }

    change()
  }

  // 显示 Picker
  const handleShowPicker = () => {
    if (!props.disabled) {
      pickerVisible.value = true
    }
  }

  // 处理 Picker 确认
  const handlePickerConfirm = (selectedValues: string[]) => {
    const val = selectedValues[0]
    if (!val) return

    // 安全获取当前选中资质的数据（用于数据迁移）
    const currentQualificationData = qualificationCode.value ? value.value[qualificationCode.value] : null
    const currentData: Partial<QualificationItem> = currentQualificationData || {}

    // 清除旧的资质数据（单选互斥）
    if (qualificationCode.value && qualificationCode.value !== val) {
      delete value.value[qualificationCode.value]
    }

    // 查找新资质的配置信息
    const newQualificationConfig = (props.elements || []).find(e => e.qualificationConfig?.qualificationCode === val)?.qualificationConfig

    // 创建新的资质数据（保留部分旧数据）
    const newData: QualificationItem = {
      qualificationCode: val,
      qualificationName: newQualificationConfig?.qualificationName || '',
      // 安全访问并保留图片和有效期数据（如果存在）
      mediaInfoList: currentData.mediaInfoList || [],
      qualificationValidity: currentData.qualificationValidity || {
        qualValidityPeriod: null,
        startTime: 0,
        endTime: 0
      }
    }

    // 更新数据
    value.value = {
      ...value.value,
      [val]: newData
    }

    pickerVisible.value = false
    change()
  }

  // 处理 Picker 取消
  const handlePickerCancel = () => {
    pickerVisible.value = false
  }

  // 处理图片列表更新
  const handleImageListUpdate = (newImages: ImageItem[]) => {
    // 清除之前的错误
    imageValidationError.value = ''

    // QualificationImage 组件已经处理了格式转换和验证，直接使用
    currentImages.value = newImages
  }

  // 处理有效期变化
  const handleValidityChange = (newValidity: any) => {
    // 清除之前的错误
    validityValidationError.value = ''

    // 验证有效期
    if (!validateValidity(newValidity)) {
      return
    }
    // 更新有效期数据
    currentValidity.value = newValidity
  }

  // 验证有效期
  const validateValidity = (validity: any) => {
    validityValidationError.value = ''

    if (props.readonly || props.disabled) {
      return true
    }

    if (!validity) {
      validityValidationError.value = '请设置有效期'
      return false
    }

    // 验证期限有效的情况
    if (validity.qualValidityPeriod === Period.NON_PERMANENT) {
      if (!validity.endTime) {
        validityValidationError.value = '请设置有效期结束时间'
        return false
      }

      const endTime = validity.endTime

      if (endTime <= Date.now()) {
        validityValidationError.value = '有效期不能早于当前时间'
        return false
      }

      // 检查30天限制
      const thirtyDaysFromNow = Date.now() + (30 * 24 * 60 * 60 * 1000)
      if (endTime <= thirtyDaysFromNow) {
        validityValidationError.value = '有效期剩余时间应大于30天'
        return false
      }
    }
    return true
  }
</script>

<style scoped lang="stylus">
.qualification-and-item-wrapper
  padding 16px
  background var(--bg)
  border-radius 12px
  margin-bottom 16px
  border 1px solid var(--border-color, #e0e0e0)
  box-shadow 0 2px 8px rgba(0, 0, 0, 0.04)

.qualification-type-header
  display flex
  align-items center
  justify-content space-between
  padding-bottom 12px

.qualification-type-label
  font-weight 500
  font-size 16px
  color var(--title)
  white-space nowrap
  margin-right 12px

.required-indicator
  color #f56565
  font-size 16px

.qualification-type-display
  padding 8px 0
  color var(--paragraph)

.qualification-text
  padding 12px 16px
  color var(--title)
  font-size 16px
  line-height 1.4
  background var(--bg-secondary, #f8f9fa)
  border-radius 8px

.picker-trigger
  display flex
  align-items center

.picker-text
  color var(--paragraph)
  font-size 16px
  line-height 1.4
  text-align center
  text-align right

.materials-header
  display flex
  align-items center
  gap 4px
  margin-bottom 8px

.materials-title
  font-weight 500
  font-size 16px
  color var(--title)

.materials-tip
  font-size 12px
  color var(--Light-Labels-Description, rgba(0, 0, 0, 0.45))
  margin-bottom 16px
  line-height 1.4

.validity-header
  display flex
  align-items center
  gap 4px
  margin-bottom 12px

.validity-title
  font-weight 500
  font-size 16px
  color var(--title)

.error-message
  color #ff4d4f
  font-size 12px
  line-height 17px
  font-weight 400
  word-break break-word

.global-error
  margin-top 16px
</style>
