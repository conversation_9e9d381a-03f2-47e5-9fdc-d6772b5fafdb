import {
  computed, ComputedRef, Ref,
  ref
} from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showToast } from '@xhs/reds-h5-next'

import { postSubjectSave } from '~/services/edith_post_subject_save'
import { getBusinessLicenseTypeFromPrincipalType } from '~/components/BusinessLicenseForm/useBaseBusinessLicenseForm'
import ROUTE_NAME from '~/constants/routename'

interface BusinessLicenseFormInstance {
  validateForm: () => Promise<boolean>
  formRef: any
  formData?: any // 添加 formData 属性
}

// 入驻模块特定的返回类型
interface UseSettleBusinessLicenseFormReturn {
  businessLicenseFormRef: Ref<BusinessLicenseFormInstance | null>
  isSubmitDisabled: ComputedRef<boolean>
  submitButtonText: ComputedRef<string>
  isSubmitting: Ref<boolean> // 添加提交loading状态
  handleFormValidation: () => void
  handleValidationError: (error: Error) => void
  handleSubmit: () => Promise<void>
  handlePrevious: () => void
}

export function useSettleBusinessLicenseForm(): UseSettleBusinessLicenseFormReturn {
  const router = useRouter()
  const store = useStore()
  // const route = useRoute()

  // 入驻模块的表单引用
  const businessLicenseFormRef = ref<BusinessLicenseFormInstance | null>(null)

  // 注意：不再在这里调用 useBusinessLicenseForm，避免重复初始化
  // 所有基础功能都由 BusinessLicenseForm 组件提供

  // 添加提交loading状态
  const isSubmitting = ref(false)

  // 获取组件表单数据的统一方法
  const getComponentFormData = () => {
    if (businessLicenseFormRef.value?.formData) {
      return businessLicenseFormRef.value.formData.value || businessLicenseFormRef.value.formData
    }
    return null
  }

  // 获取表单数据的方法
  const getFormData = () => {
    const componentFormData = getComponentFormData()
    if (componentFormData) {
      return componentFormData
    }

    // 如果组件数据不存在，返回空对象
    return {}
  }

  // 计算提交按钮是否禁用
  const isSubmitDisabled = computed(() => {
    const requiredFields = ['businessLicense', 'uniformCreditCode', 'companyName', 'businessAddress', 'validityPeriod']
    const formData = getComponentFormData()
    if (!formData) return true // 如果没有表单数据，禁用提交

    // 检查是否有必填字段为空
    const hasEmptyField = requiredFields.some(field => {
      const value = formData[field]
      return !value || (typeof value === 'string' && value.trim() === '')
    })

    return hasEmptyField
  })

  // 计算提交按钮文本
  const submitButtonText = computed(() => '继续提交材料')

  const handleFormValidation = async () => {
    if (!businessLicenseFormRef.value) {
      // 如果没有表单引用，直接提交
      handleSubmit()
      return false
    }

    try {
      // 进行表单验证
      const isValid = await businessLicenseFormRef.value.validateForm()
      if (isValid) {
        handleSubmit()
      }
    } catch (error) {
      // handleValidationError(error as Error)
    }
  }

  // 表单验证失败
  const handleValidationError = () => {
    // console.log('表单验证失败:', error.message)
  }

  // 处理提交 - 入驻模块直接调用API
  const handleSubmit = async () => {
    if (isSubmitDisabled.value) {
      showToast({
        message: '请完善必填信息',
        duration: 2000
      })
      return
    }

    isSubmitting.value = true // 开始提交loading

    try {
      const formData = getFormData()
      let payload: any
      if (formData.useProfessionalQualification) {
        payload = {
          uniformCreditCode: formData.uniformCreditCode || '',
          companyRegisteredAddress: formData.businessAddress || formData.companyRegisteredAddress || '',
          companyName: formData.companyName || '',
          businessLicense: {
            qualificationCode: '3',
            qualificationName: '营业执照',
            startTime: typeof formData.validityPeriod?.startTime === 'number'
              ? formData.validityPeriod.startTime
              : (formData.validityPeriod?.startTime ? +formData.validityPeriod.startTime : 0),
            endTime: typeof formData.validityPeriod?.endTime === 'number'
              ? formData.validityPeriod.endTime
              : 0,
            permanent: formData.validityPeriod?.qualValidityPeriod === 0,
            fileAttachmentList: [{
              uploaderInfoModel: {
                url: formData.businessLicense,
                isSecret: false,
                cloudType: 0
              }
            }]
          },

        }
      } else {
        const fileAttachment = formData.businessLicenseImage?.[0] || {
          uploaderInfoModel: {
            url: formData.businessLicense,
            isSecret: false,
            cloudType: 0
          }
        }
        payload = {
          uniformCreditCode: formData.uniformCreditCode || '',
          companyRegisteredAddress: formData.businessAddress || formData.companyRegisteredAddress || '',
          companyName: formData.companyName || '',
          businessLicense: {
            qualificationCode: '3',
            qualificationName: '营业执照',
            startTime: typeof formData.validityPeriod?.startTime === 'number'
              ? formData.validityPeriod.startTime
              : (formData.validityPeriod?.startTime ? +formData.validityPeriod.startTime : 0),
            endTime: typeof formData.validityPeriod?.endTime === 'number'
              ? formData.validityPeriod.endTime
              : 0,
            permanent: formData.validityPeriod?.qualValidityPeriod === 0,
            fileAttachmentList: [fileAttachment]
          },
          registeredAddressProvince: formData.registeredAddressProvince || '',
          registeredAddressCity: formData.registeredAddressCity || '',
          registeredAddressStreet: formData.registeredAddressStreet || '',
          principalType: formData.principalType,
          authorizationName: formData.authorizationName || ''
        }
      }
      // console.log(payload, 'payload')
      await postSubjectSave({
        merchantCompanyDraft: payload
      })

      // 同步数据到store，用于返回时回显
      const storeUpdateData: any = {
        // 公司名称
        companyName: formData.companyName,
        // 详细地址（经营地址）
        businessAddress: formData.businessAddress || formData.companyRegisteredAddress || '',
        // 营业执照类型转换：基于 principalType 计算
        businessLicenseType: getBusinessLicenseTypeFromPrincipalType(formData.principalType),
        // 统一社会信用代码
        usci: formData.uniformCreditCode,
        // 营业执照有效期信息
        businessLicenseValidity: formData.validityPeriod ? ({
          qualValidityPeriod: formData.validityPeriod.qualValidityPeriod,
          startTime: formData.validityPeriod.startTime || 0,
          endTime: formData.validityPeriod.endTime || 0
        }) : undefined,
        // 是否使用专业号资质
        useProfessionalQualification: formData.useProfessionalQualification,
      }

      // 处理营业执照图片信息
      if (formData.useProfessionalQualification) {
        // 使用专业号资质时，直接存储URL
        storeUpdateData.businessLicenseImage = {
          url: formData.businessLicense,
        }
      } else {
        // 不使用专业号资质时，存储完整的图片信息
        const businessLicenseImage = formData.businessLicenseImage?.[0] || {}
        storeUpdateData.businessLicenseImage = {
          url: businessLicenseImage.uploaderInfoModel?.url || formData.businessLicense,
          width: businessLicenseImage?.width,
          height: businessLicenseImage?.height
        }
      }

      // 同步到ClaimShopStore
      store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', storeUpdateData)

      // 跳转到下一步
      router.push({
        name: ROUTE_NAME.SUPPLEMENTARY_QUALIFICATIONS_SETTLE,
      })
    } catch (error) {
      showToast({
        message: (error as Error)?.message || '提交失败',
        duration: 2000
      })
    } finally {
      isSubmitting.value = false // 结束提交loading
    }
  }

  // 返回上一步
  const handlePrevious = () => {
    router.go(-1)
  }

  return {
    businessLicenseFormRef,

    // 入驻模块特定功能
    isSubmitDisabled,
    submitButtonText,
    isSubmitting, // 暴露提交loading状态
    handleFormValidation,
    handleValidationError,
    handleSubmit,
    handlePrevious,
  }
}
