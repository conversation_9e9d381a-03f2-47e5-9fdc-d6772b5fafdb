<template>
  <div class="settle-business-license-page">
    <!-- 头部组件 -->
    <header>
      <Header>
        <template #right>
          <div class="claim-shop-header" @click="handleCustomerServiceClick">
            <Icon icon-name="StoreChat" size="16"></Icon>
            <Text class="customer">客服</Text>
          </div>
        </template>
      </Header>
    </header>
    <Steps type="CLAIM_SHOP" />
    <main>
      <LanguageProvider>
        <BusinessLicenseForm
          :key="`settle-${$route.name}-${$route.path}`"
          ref="businessLicenseFormRef"
          :is-settle="true"
          @submitSuccess="handleSubmit"
          @submitError="handleValidationError"
        />
      </LanguageProvider>
    </main>

    <!-- 入驻模块特有的上一步/下一步按钮 -->
    <footer>
      <Button
        size="large"
        :style="{width: '173px'}"
        @click="handlePrevious"
      >
        上一步
      </Button>
      <Button
        type="primary"
        size="large"
        :style="{width: '173px'}"
        :variant="isSubmitDisabled ? 'disabled' : 'fill'"
        :disabled="isSubmitDisabled"
        @click="handleFormValidation"
      >
        {{ submitButtonText }}
      </Button>
    </footer>
  </div>
</template>

<script setup lang="ts">
  import {
    Icon,
    Button,
    Text,
    LanguageProvider
  } from '@xhs/reds-h5-next'

  // 组件
  import Header from '~/components/header/index.vue'
  import Steps from '~/components/Steps/index.vue'

  import BusinessLicenseForm from '~/components/BusinessLicenseForm/index.vue'

  // composable - 入驻模块专用
  import { useSettleBusinessLicenseForm } from './composables/useSettleBusinessLicenseForm'

  // 方法
  import { jumpContactXhsSupport } from '~/utils/shopPage'

  // 初始化vuex

  // 使用入驻模块专用的 composable
  const {
    // 数据
    businessLicenseFormRef,

    // 计算属性
    isSubmitDisabled,
    submitButtonText,

    // 方法
    handleSubmit,
    handlePrevious,
    handleFormValidation,
    handleValidationError,
  } = useSettleBusinessLicenseForm()

  // 新增 method
  const handleCustomerServiceClick = () => {
    jumpContactXhsSupport()
  }

</script>

<style lang="stylus" scoped>
// 页面布局
.settle-business-license-page
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5
  padding-top 24px
  // iOS 安全区域适配 - 顶部
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

  header
    flex 0 0 auto
    .customer
      color rgba(0, 0, 0, 0.8)
    .claim-shop-header
      display flex
      align-items center
      justify-content center

  main
    flex 1 1 0
    min-height 0
    padding 0 16px
    overflow auto
    -webkit-overflow-scrolling touch

  footer
    display flex
    align-items center
    justify-content space-between
    flex-shrink 0
    min-height 50px
    gap 15.5px
    padding 8px 16px
    // iOS 安全区域适配 - 底部
    @supports (padding-bottom: constant(safe-area-inset-bottom))
      padding-bottom constant(safe-area-inset-bottom)
    @supports (padding-bottom: env(safe-area-inset-bottom))
      padding-bottom env(safe-area-inset-bottom)
</style>
