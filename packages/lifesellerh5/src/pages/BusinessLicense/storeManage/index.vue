<template>
  <div class="store-manage-business-license-page">
    <header>
      <Header>
        <template #right>
          <div class="claim-shop-header" @click="handleCustomerServiceClick">
            <Icon icon-name="StoreChat" size="16"></Icon>
            <Text class="customer">客服</Text>
          </div>
        </template>
      </Header>
    </header>
    <Steps type="CLAIM_SHOP" />
    <main>
      <LanguageProvider>
        <BusinessLicenseForm
          :key="`store-manage-${$route.name}-${$route.path}-${isLightClaim ? 'light' : 'local'}`"
          ref="businessLicenseFormRef"
          :is-light-claim="isLightClaim"
          @submitSuccess="handleSubmit"
          @submitError="handleValidationError"
        />
      </LanguageProvider>
    </main>

    <footer>
      <Button
        size="large"
        :style="{width: '173px'}"
        @click="handlePrevious"
      >
        上一步
      </Button>
      <Button
        type="primary"
        size="large"
        :style="{width: '173px'}"
        :variant="isSubmitDisabled ? 'disabled' : 'fill'"
        :disabled="isSubmitDisabled"
        @click="handleFormValidation"
      >
        {{ submitButtonText }}
      </Button>
    </footer>
  </div>
</template>

<script setup lang="ts">

  import {
    Icon,
    Button,
    Text,
    LanguageProvider
  } from '@xhs/reds-h5-next'
  import { computed } from 'vue'
  import { useStore } from 'vuex'

  // 组件
  import Header from '~/components/header/index.vue'
  import Steps from '~/components/Steps/index.vue'

  import BusinessLicenseForm from '~/components/BusinessLicenseForm/index.vue'

  // composable
  import { useStoreManageBusinessLicenseForm } from './composables/useStoreManageBusinessLicenseForm'

  // 方法
  import { jumpContactXhsSupport } from '~/utils/shopPage'

  import '~/assets/svg/iconRight2.svg'
  import { ClaimTypeEnum } from '~/enum/shop'

  // 初始化vuex
  const store = useStore()

  const userInfoStore = store.state.ShopUserInfoStore
  // 判断是否为轻认领
  const isLightClaim = computed(() => userInfoStore.customClaimType === ClaimTypeEnum.LIGHT_CLAIM)

  // 使用门店管理模块专用的 composable
  const {
    businessLicenseFormRef,

    // 计算属性
    isSubmitDisabled,
    submitButtonText,

    // 方法
    handlePrevious,
    handleSubmit,
    handleFormValidation,
    handleValidationError,
  } = useStoreManageBusinessLicenseForm()

  // 客服点击处理
  const handleCustomerServiceClick = () => {
    jumpContactXhsSupport()
  }

</script>

<style lang="stylus" scoped>
// 页面布局
.store-manage-business-license-page
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5
  padding-top 24px
  // iOS 安全区域适配 - 顶部
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

  header
    flex 0 0 auto
    .customer
      color rgba(0, 0, 0, 0.8)

    .claim-shop-header
      display flex
      align-items center
      justify-content center

  main
    flex 1 1 0
    min-height 0
    padding 0 16px
    overflow auto
    -webkit-overflow-scrolling touch

  footer
    display flex
    align-items center
    justify-content space-between
    flex-shrink 0
    min-height 50px
    gap 15.5px
    padding 8px 16px
    // iOS 安全区域适配 - 底部
    @supports (padding-bottom: constant(safe-area-inset-bottom))
      padding-bottom constant(safe-area-inset-bottom)
    @supports (padding-bottom: env(safe-area-inset-bottom))
      padding-bottom env(safe-area-inset-bottom)
</style>
