import {
  computed, ComputedRef, Ref, ref
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { showToast, ToastType } from '@xhs/reds-h5-next'
import { getBusinessLicenseTypeFromPrincipalType } from '~/components/BusinessLicenseForm/useBaseBusinessLicenseForm'

import { IPostPoiSubmitapplyPayload, postPoiSubmitapply } from '~/services/edith_post_poi_submitapply'

import { getErrorMessage } from '~/utils/help'
import ROUTE_NAME from '~/constants/routename'

// 数据
import { ClaimTypeEnum, PoiClaimApplyType } from '~/enum/shop'

interface BusinessLicenseFormInstance {
  validateForm: () => Promise<boolean>
  formRef: any
  formData?: any // 添加 formData 属性
}

// 门店管理模块特定的返回类型
interface UseStoreManageBusinessLicenseFormReturn {
  businessLicenseFormRef: Ref<BusinessLicenseFormInstance | null>
  isSubmitDisabled: ComputedRef<boolean>
  submitButtonText: ComputedRef<string>
  isSubmitting: Ref<boolean> // 添加提交loading状态
  handleFormValidation: () => void
  handleValidationError: (error: Error) => void
  handlePrevious: () => void
  handleSubmit: () => Promise<void>
}

export function useStoreManageBusinessLicenseForm(): UseStoreManageBusinessLicenseFormReturn {
  const route = useRoute()
  const router = useRouter()
  const store = useStore()
  const claimTypeValue = computed(() => store.state.ShopUserInfoStore.customClaimType === ClaimTypeEnum.LIGHT_CLAIM) // 判断是否为轻认领

  const businessLicenseFormRef = ref<BusinessLicenseFormInstance | null>(null) // 门店管理模块的表单引用

  // 注意：不再在这里调用 useBusinessLicenseForm，避免重复初始化
  // 所有基础功能都由 BusinessLicenseForm 组件提供

  // 添加提交loading状态
  const isSubmitting = ref(false)

  // 获取组件表单数据的统一方法
  const getComponentFormData = () => {
    if (businessLicenseFormRef.value?.formData) {
      return businessLicenseFormRef.value.formData.value || businessLicenseFormRef.value.formData
    }
    return null
  }

  // 计算提交按钮是否禁用
  const isSubmitDisabled = computed(() => {
    const requiredFields = ['businessLicense']

    if (claimTypeValue.value) {
      // console.log('轻认领')
    } else {
      requiredFields.push('validityPeriod', 'businessAddress', 'uniformCreditCode', 'companyName')
    }

    const formData = getComponentFormData()

    if (!formData) return true // 如果没有表单数据，禁用提交

    // 检查是否有必填字段为空
    const hasEmptyField = requiredFields.some(field => {
      const value = formData[field]
      return !value || (typeof value === 'string' && value.trim() === '')
    })

    return hasEmptyField
  })

  // 判断是否开通本地账户，如果是否，则按钮为"提交审核"，如果为是，则按钮为"继续提交材料"
  const submitButtonText = computed(() => (
    claimTypeValue.value ? '提交审核' : '继续提交材料'
  ))

  const handleFormValidation = async () => {
    if (!businessLicenseFormRef.value) {
      // 如果没有表单引用，直接提交
      handleSubmit()
      return false
    }

    try {
      // 进行表单验证
      const isValid = await businessLicenseFormRef.value.validateForm()
      if (isValid) {
        handleSubmit()
      }
    } catch (error) {
      handleValidationError()
    }
  }
  // 获取表单数据的方法
  const getFormData = () => {
    const componentFormData = getComponentFormData()
    if (componentFormData) {
      return componentFormData
    }

    // 如果组件数据不存在，返回空对象
    return {}
  }

  // 处理提交 - 门店管理模块存储到 store
  const handleSubmit = async () => {
    if (isSubmitDisabled.value) {
      showToast({
        message: '请完善必填信息',
        duration: 2000
      })
      return
    }

    const formData = getFormData()

    isSubmitting.value = true // 开始提交loading

    // 轻认领 - 直接提交审核
    if (claimTypeValue.value) {
      try {
        const {
          claimStorePageFormData
        } = store.state.ClaimShopStore
        // 合并表单数据和 store 数据
        const payload: IPostPoiSubmitapplyPayload = {
          id: claimStorePageFormData.id,
          poiId: claimStorePageFormData.poiId,
          poiName: claimStorePageFormData.poiName,
          poiAddress: claimStorePageFormData.poiAddress,
          poiTelephones: claimStorePageFormData.poiTelephones,
          refType: claimStorePageFormData.refType,
          refSubType: claimStorePageFormData.refSubType,
          auditStatus: claimStorePageFormData.auditStatus,
          certificateQual: claimStorePageFormData.certificateQual,
          licenseChgQual: claimStorePageFormData.licenseChgQual,
          isSubmit: 1,
          auditRemark: claimStorePageFormData.auditRemark,
          // 使用表单中的营业执照数据
          companyLicense: formData.businessLicense
            ? [formData.businessLicense]
            : (claimStorePageFormData.companyLicense || []),
          companyName: formData.companyName || claimStorePageFormData.companyName,
          isUseProCertification: formData?.useProfessionalQualification ?? false,
        }
        if (!formData.useProfessionalQualification) {
          const {
            url,
            filename,
            fileType
          } = formData.businessLicenseImage[0] || formData.businessLicense || {}
          payload.certificateQual = {
            fileAttachments: [{
              url,
              filename,
              fileType
            }]
          }
        }

        const res = await postPoiSubmitapply(payload)

        if (res.applyId) {
          const routeName = store.state.ShopUserInfoStore.customClaimType === ClaimTypeEnum.MERCHANT_ENTER ? ROUTE_NAME.AUDIT_RESULTS_SETTLE : ROUTE_NAME.AUDIT_RESULTS

          const shopBizApplyTypeNumber = ref<number>(0)
          switch (store.state.ShopUserInfoStore.customClaimType) {
            case ClaimTypeEnum.LIGHT_CLAIM:
              shopBizApplyTypeNumber.value = PoiClaimApplyType.PRO_CLAIM_APPLY
              break
            case ClaimTypeEnum.LOCAL_CLAIM:
              shopBizApplyTypeNumber.value = PoiClaimApplyType.POI_CLAIM_APPLY
              break
            default:
              return
          }
          router.replace({
            name: routeName,
            query: {
              ...route.query,
              fullscreen: 'true',
              shopId: res.poiId,
              applyId: res.applyId,
              shopBizApplyType: shopBizApplyTypeNumber.value,
              type: 'claim',
            },
            params: { ...route.params },
          })
        }
      } catch (error) {
        showToast({
          type: ToastType.ToastBuiltInType.TEXT,
          message: getErrorMessage(error, '提交失败，请重试'),
          duration: 2000
        })
      } finally {
        isSubmitting.value = false // 结束提交loading
      }
    } else {
      // 本地认领 - 数据存储到 store
      try {
        const formData = getFormData()
        // 将表单数据映射到 store 格式
        const storeUpdateData: any = {
          // 公司名称
          companyName: formData.companyName,
          // 详细地址
          businessAddress: formData.businessAddress || '',
          // 营业执照类型转换：基于 principalType 计算
          businessLicenseType: getBusinessLicenseTypeFromPrincipalType(formData.principalType),
          // 营业执照图片信息
          // 统一社会信用代码
          usci: formData.uniformCreditCode,

          // 营业执照有效期信息
          businessLicenseValidity: formData.validityPeriod ? ({
            qualValidityPeriod: formData.validityPeriod.qualValidityPeriod,
            startTime: formData.validityPeriod.startTime || 0,
            endTime: formData.validityPeriod.endTime || 0
          }) : undefined,
          useProfessionalQualification: formData.useProfessionalQualification,
        }
        if (formData.useProfessionalQualification) {
          // 营业执照图片信息
          storeUpdateData.businessLicenseImage = {
            url: formData.businessLicense,
          }
        } else {
          // 营业执照图片信息
          const businessLicenseImage = formData.businessLicenseImage[0] || formData.businessLicense || {}
          storeUpdateData.businessLicenseImage = {
            url: businessLicenseImage.uploaderInfoModel?.url || formData.businessLicense,
            width: businessLicenseImage?.width,
            height: businessLicenseImage?.height
          }
        }

        // 使用正确的 mutation 更新 store
        store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', storeUpdateData)

        // TODO：新版路由
        // 跳转到下一步或返回列表
        router.push({
          name: ROUTE_NAME.SUPPLEMENTARY_QUALIFICATIONS_STORE_CLAIM
        })
      } catch (error) {
        showToast({
          message: (error as Error)?.message || '保存失败',
          duration: 2000
        })
      } finally {
        isSubmitting.value = false // 结束提交loading
      }
    }
  }

  // 表单验证失败
  const handleValidationError = () => {
    // showToast({
    //   message: error.message || '表单验证失败',
    //   duration: 2000
    // })
  }

  // 返回上一步
  const handlePrevious = () => {
    router.go(-1)
  }

  return {
    businessLicenseFormRef,

    // 门店管理模块特定功能
    isSubmitDisabled,
    isSubmitting, // 暴露提交loading状态
    handleFormValidation,
    handleValidationError,
    submitButtonText,
    handlePrevious,
    handleSubmit,
  }
}
