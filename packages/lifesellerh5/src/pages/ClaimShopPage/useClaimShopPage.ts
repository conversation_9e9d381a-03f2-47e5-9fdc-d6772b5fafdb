// vue
import {
  ref, reactive, computed, onMounted, watch
} from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

// 依赖
import {
  showToast, ToastType,
} from '@xhs/reds-h5-next'
import { cloneDeep } from 'lodash'

// 数据
import ROUTE_NAME from '~/constants/routename'
import {
  formItems as originFormItems,
  formRules,
  defaultValueMap,
  FormValueKeyEnum,
  FormItemTypeEnum,
  poiShopTipMap,
} from './content'
import {
  ProStatusEnum,
  ClaimTypeEnum,
  PoiClaimApplyType,
  QueryBusinessTypeEnum,
  SavePoiEntryEnum,
  MerchantApplyStatusEnum
} from '~/enum/shop'
import {
  AlertTypeEnum
} from './enum'
import { UPGRADE_ENTERPRISE, ACTIVATE_PRO } from '~/constants/link'

// 接口
import {
  getPreCheck,
  getApplyQuery,
  getDraftQuery,
  postQueryClaimInfo,
  postApiRedlife,
  getCanClaimPoiMerchant,
  postDraftSave,
} from '~/services/shop'

// 方法
import { getQueryString } from '~/utils/help'
import { jumpContactXhsSupport } from '~/utils/shopPage'

export function useClaimShopPage() {
  // ==============初始化==============
  // 初始化路由
  const route = useRoute()
  const router = useRouter()

  // route.query参数
  const applyId = getQueryString(route.query.applyId)
  const shopId = getQueryString(route.query.shopId)
  // 列表跳转时的参数判断是不是轻、本地
  const shopBizApplyType = getQueryString(route.query.shopBizApplyType)
  // 门店管理列表的补充资质并开店按钮的跳转
  const businessType = getQueryString(route.query.businessType)

  const secondChannel = getQueryString(route.query.secondChannel)

  // 独立入驻使用
  const isEdit = getQueryString(route.query.isEdit)

  // 初始化vuex
  const store = useStore()

  const claimStorePageFormData = computed(() =>
    store.state.ClaimShopStore.claimStorePageFormData)

  const userInfo = computed(() =>
    store.state.ShopUserInfoStore.userInfo)

  const shopApplyState = computed(() =>
    store.state.ShopApplyStore.shopApplyState)

  // 当前参数
  const prevRouteParams = computed(() =>
    store.state.ClaimShopStore.prevRouteParams)

  // 初始化form表单响应式对象
  const formModel = reactive({})

  // 动态初始化每个表单项
  const formItems = cloneDeep(originFormItems)

  // ==============工具函数==============
  // 统一错误处理
  const handleError = (error, context = '') => {
    console.error(`${context}:`, error)
    const message = typeof error === 'object' && error && 'message' in error
      ? (error as any).message
      : String(error)

    showToast({
      type: ToastType.ToastBuiltInType.TEXT,
      message
    })
  }

  // 数据源优先级判断
  const getDataSource = () => {
    const hasStoreData = !isFormModelEmptyFromStore(formItems, formRules, store)

    return {
      // 只有ID不同或store中没有数据时才需要调用API
      needsApiCall: !hasStoreData,
      hasStoreData
    }
  }

  // 表单数据映射器
  const createFormModelMapper = (formItems, defaultValueMap) => source => {
    const result = {}
    formItems.forEach(item => {
      const key = item.valueKey

      // 处理特殊字段映射
      if (key === FormValueKeyEnum.PoiShopInfo) {
        result[key] = {
          poiId: source?.poiId ?? '',
          poiName: source?.poiName ?? '',
          addressDetail: source?.addressDetail ?? ''
        }
      } else if (item.customMapper && typeof item.customMapper === 'function') {
        // 支持自定义映射函数
        result[key] = item.customMapper(source)
      } else {
        // 默认映射逻辑
        result[key] = (source && source[key] !== undefined)
          ? cloneDeep(source[key])
          : (defaultValueMap[item.type] ?? '')
      }
    })
    return result
  }

  // 门店地址底部提示
  const poiShopInfoTip = (valueKey: FormValueKeyEnum | string, value: any) => {
    let tip = ''
    if (valueKey === FormValueKeyEnum.PoiShopInfo) {
      tip = value ? poiShopTipMap.selected : poiShopTipMap.empty
    }
    return tip
  }

  // ==============表单事件==============
  // 表单点击事件
  const formItemClick = (valueKey: string) => {
    switch (valueKey) {
      case FormValueKeyEnum.PoiShopInfo:
        pushStoreSearch()
        break
      default:
        break
    }
  }

  // 跳转到选择门店页面
  const pushStoreSearch = () => {
    router.push({
      name: ROUTE_NAME.STORE_SEARCH,
      query: { ...route.query, fullscreen: 'true' },
      params: { ...route.params },
    })
  }

  // ==============表单校验==============
  const checkForm = (formModel, formRules) => {
    for (const key in formRules) {
      // 只校验自身属性，过滤原型上的键
      if (Object.prototype.hasOwnProperty.call(formRules, key)) {
        const rules = formRules[key]
        const value = formModel[key]
        for (const rule of rules) {
          // required 校验
          if (rule.required) {
            if (typeof value === 'object' && rule.requiredFields) {
              if (!rule.requiredFields.every(k => !!value && !!value[k])) {
                return rule.message || '请完善信息'
              }
            } else if (!value) {
              return rule.message || '请填写'
            }
          }
          // 自定义 validator 支持
          if (rule.validator) {
            const res = rule.validator(value, formModel)
            if (typeof res === 'string') return res
            if (res === false) return rule.message || '校验未通过'
          }
        }
      }
    }
    return true
  }

  // ==============弹层事件==============
  const alertShow = ref<boolean>(false)
  const showCancelButton = ref<boolean>(false)
  const message = ref<string>('')
  const alertTitle = ref<string>('')
  const confirmText = ref<string>('')
  const link = ref<string>('')
  // 添加弹窗类型变量
  const alertType = ref('')

  // 更新弹窗参数事件
  const uploadAlertData = config => {
    alertType.value = config.type
    alertTitle.value = config.title || ''
    message.value = config.message
    confirmText.value = config.confirmText
    showCancelButton.value = config.showCancelButton || false
    link.value = config.link || ''
    alertShow.value = true
  }

  // 弹窗确认事件
  const confirmAlert = () => {
    switch (alertType.value) {
      case AlertTypeEnum.ACTIVATE_PRO:
      case AlertTypeEnum.UPGRADE_ENTERPRISE:
      case AlertTypeEnum.PRO_EXPIRE:
        window.open(link.value)
        alertShow.value = false
        break

      case AlertTypeEnum.POI_CLAIMED:
        // 联系客服逻辑
        if (link.value) {
          window.open(link.value)
        }
        alertShow.value = false
        break

      case AlertTypeEnum.CONTACT_SERVICE:

        break

      default:
        alertShow.value = false
        break
    }
  }

  // 弹窗取消事件
  const cancelAlert = () => {
    alertShow.value = false
    router.go(-1)
  }

  // ==============下一步按钮==============
  // 按钮的isSubmitDisabled
  const isSubmitDisabled = computed(() => checkForm(formModel, formRules) !== true)

  // 校验是否可以入驻
  const getCanClaimPoiMerchantApi = async () => {
    try {
      if (store.state.ShopUserInfoStore.customClaimType === ClaimTypeEnum.MERCHANT_ENTER) {
        router.push({
          name: ROUTE_NAME.BUSINESS_LICENSE_SETTLE,
          query: {
            ...route.query,
            fullscreen: 'true',
          },
          params: { ...route.params },
        })
      } else {
        router.push({
          name: ROUTE_NAME.BUSINESS_LICENSE_STORE_MANAGE,
          query: {
            ...route.query,
            fullscreen: 'true',
          },
          params: { ...route.params },
        })
      }
    } catch (error) {
      handleError(error, '信息保存')
    }
  }

  // 1、入驻：保存入驻信息。2、补充资质并开店按钮进入时，需要手动保存poi信息
  const postDraftSaveApi = async () => {
    try {
      const params = {
        poiId: claimStorePageFormData.value.poiId,
        enterChannel: {
          firstChannel: 'APP',
          secondChannel: businessType === QueryBusinessTypeEnum.SUPPLEMENT_AND_OPEN ? SavePoiEntryEnum.FILL_QUALIFICATION : secondChannel
        }
      }
      await postDraftSave(params)
      getCanClaimPoiMerchantApi()
    } catch (error) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: typeof error === 'object' && error && 'message' in error
          ? (error as any).message
          : String(error),
      })
    }
  }

  // 下一步按钮事件
  const nextStep = async () => {
    const validateResult = checkForm(formModel, formRules)
    if (validateResult !== true) {
      handleError(validateResult, '表单校验失败')
      return
    }

    // 门店管理里的补充资质并开店 ||  独立入驻才会去保存
    if (businessType === QueryBusinessTypeEnum.SUPPLEMENT_AND_OPEN || store.state.ShopUserInfoStore.customClaimType === ClaimTypeEnum.MERCHANT_ENTER) {
      await postDraftSaveApi()
    } else {
      getCanClaimPoiMerchantApi()
    }
  }

  // ==============数据回流到vuex中==============

  // 检查参数是否发生变化
  const checkParamsChange = (newApplyId, newShopId) => {
    const prevApplyId = prevRouteParams.value.applyId
    const prevShopId = prevRouteParams.value.shopId

    // 如果之前有参数且参数发生变化，返回 true
    if (prevApplyId !== null || prevShopId !== null) {
      return (prevApplyId !== newApplyId) || (prevShopId !== newShopId)
    }

    return false
  }

  // 清空 store 数据
  const resetStoreData = () => {
    // 重置所有相关 store 数据
    store.commit('ClaimShopStore/RESET_CLAIM_STORE_PAGE_FORM_DATA_STORE')
    store.commit('ClaimShopStore/RESET_PREV_ROUTE_PARAMS_STORE')
    store.commit('ShopApplyStore/RESET_SHOP_APPLY_STATE')
    store.commit('ShopUserInfoStore/RESET_USER_INFO')

    // 清空表单数据
    Object.keys(formModel).forEach(key => {
      delete formModel[key]
    })
  }

  // 更新路由参数到 store
  const updateRouteParams = (newApplyId, newShopId) => {
    store.commit('ClaimShopStore/UPDATE_PREV_ROUTE_PARAMS_STORE', {
      applyId: newApplyId,
      shopId: newShopId
    })
  }

  // applyid和shopid发生变化时需要清空数据
  watch(
    () => [route.query.applyId, route.query.shopId],
    ([newApplyId, newShopId]) => {
      const currentApplyId = getQueryString(newApplyId)
      const currentShopId = getQueryString(newShopId)

      // 检查参数是否变化
      if (checkParamsChange(currentApplyId, currentShopId)) {
        // 清空数据
        resetStoreData()

        // 更新参数
        updateRouteParams(currentApplyId, currentShopId)

        // 重新初始化
        setTimeout(() => {
          init()
        }, 0)
      } else {
      // 首次进入，只更新参数
        updateRouteParams(currentApplyId, currentShopId)
      }
    },
    {
      immediate: true,
      deep: false
    }
  )

  // ==============初始化请求数据==============
  const loading = ref<boolean>(false)

  const getFieldValue = (key, type, store) => {
    if (type === FormItemTypeEnum.PoiShopInfo) {
      return store.state.ClaimShopStore.claimStorePageFormData
    }
    return store.state.ClaimShopStore.claimStorePageFormData[key]
  }

  const isFormModelEmptyFromStore = (formItems, formRules, store) => formItems.every(item => {
    const value = getFieldValue(item.valueKey, item.type, store)
    if (item.type === FormItemTypeEnum.PoiShopInfo) {
      const requiredFields = formRules[item.valueKey]?.[0]?.requiredFields || []
      return requiredFields.every(field => !value || !value[field]) // 任一required字段为空就认空
    }
    if (Array.isArray(value)) return value.length === 0
    if (typeof value === 'object' && value !== null) return Object.keys(value).length === 0
    return value === '' || value === undefined || value === null
    || value === 0 || value === false
  })

  // 使用新的映射器
  const formModelMapper = createFormModelMapper(formItems, defaultValueMap)

  // 优化后的表单数据填充函数
  const fillFormModelFromSource = (formModel, source) => {
    const mappedData = formModelMapper(source)
    Object.assign(formModel, mappedData)
  }

  // 共用：前置预检接口
  const getPreCheckApi = async () => {
    try {
      const res = await getPreCheck()
      store.commit('ShopUserInfoStore/UPDATE_USER_INFO', res)
      switch (res.proStatus) {
        case ProStatusEnum.NONE_PRO:
          uploadAlertData({
            type: AlertTypeEnum.ACTIVATE_PRO,
            title: '',
            message: '认领地点需要先开通专业号, 点击"前往开通"后跳转至专业号开通页',
            confirmText: '前往开通',
            showCancelButton: true,
            link: ACTIVATE_PRO
          })
          break

        case ProStatusEnum.PRO_NOT_ALLOWED:
          uploadAlertData({
            type: AlertTypeEnum.UPGRADE_ENTERPRISE,
            title: '',
            message: '暂不支持个人专业号认领门店，请升级为企业专业号后重试',
            confirmText: '前往开通',
            showCancelButton: true,
            link: UPGRADE_ENTERPRISE
          })
          break

        case ProStatusEnum.PRO_EXPIRE:
          uploadAlertData({
            type: AlertTypeEnum.PRO_EXPIRE,
            title: '',
            message: '认领门店需要与专业号关联，但当前专业号已过期，请续期专业号后重试',
            confirmText: '前往续期',
            showCancelButton: true,
            link: ACTIVATE_PRO
          })
          break

        default:
          break
      }
      switch (res.proStatus) {
        case ProStatusEnum.NONE_PRO:

          alertShow.value = true
          showCancelButton.value = true
          message.value = '认领地点需要先开通专业号, 点击"前往开通"后跳转至专业号开通页'
          confirmText.value = '前往开通'
          link.value = ACTIVATE_PRO
          break

        case ProStatusEnum.PRO_NOT_ALLOWED:
          alertShow.value = true
          showCancelButton.value = true
          message.value = '暂不支持个人专业号认领门店，请升级为企业专业号后重试'
          confirmText.value = '前往开通'
          link.value = UPGRADE_ENTERPRISE
          break

        case ProStatusEnum.PRO_EXPIRE:
          alertShow.value = true
          showCancelButton.value = true
          message.value = '认领门店需要与专业号关联，但当前专业号已过期，请续期专业号后重试'
          confirmText.value = '前往续期'
          link.value = ACTIVATE_PRO
          break

        default:
          break
      }
    } catch (error) {
      handleError(error, '前置预检')
    }
  }

  // 入驻进度
  const getApplyQueryApi = async () => {
    try {
      const res = await getApplyQuery()
      store.commit('ShopApplyStore/UPDATE_SHOP_APPLY_STATE', res)

      // 需要根据轻、本地AUDIT_RESULTS，独立入驻AUDIT_RESULTS_SETTLE， 跳转对应状态页面
      const routerName = userInfo.value.claimType === ClaimTypeEnum.MERCHANT_ENTER ? ROUTE_NAME.AUDIT_RESULTS_SETTLE : ROUTE_NAME.AUDIT_RESULTS
      // const shopBizApplyTypeNumber = ref<number>(0)
      // switch (userInfo.value.claimType) {
      //   case ClaimTypeEnum.LIGHT_CLAIM:
      //     shopBizApplyTypeNumber.value = PoiClaimApplyType.PRO_CLAIM_APPLY
      //     break
      //   case ClaimTypeEnum.LOCAL_CLAIM:
      //     shopBizApplyTypeNumber.value = PoiClaimApplyType.POI_CLAIM_APPLY
      //     break
      //   default:
      //     break
      // }

      // 后续再说，这一期只有审核中的一个状态跳转
      switch (res.applyStatus) {
        case MerchantApplyStatusEnum.TO_SP_AUDIT:

          router.replace({
            name: routerName,
            query: {
              ...route.query,
              // applyId: res.id,
              fullscreen: 'true',
              // shopBizApplyType: shopBizApplyTypeNumber.value,
            },
            params: { ...route.params },
          })
          break
        case MerchantApplyStatusEnum.TO_SUBMIT:
          uploadAlertData({
            type: AlertTypeEnum.DRAFT_CONTINUE_EDIT,
            message: '检测到存在未提交草稿，是否继续编辑？',
            confirmText: '确定',
            showCancelButton: false,
          })
          break
        // 初始化 | 预处理中
        // case MerchantApplyStatusEnum.INIT:
        // case MerchantApplyStatusEnum.PRE_HANDLING:
        //   router.push({
        //     name: ROUTE_NAME.BUSINESS_LICENSE_STORE_MANAGE,
        //     query: {
        //       ...route.query,
        //       fullscreen: 'true',
        //     },
        //     params: { ...route.params },
        //   })
        //   return
        // //  审核通过 | 入驻完成
        // case MerchantApplyStatusEnum.AUDIT_PASS:
        // case MerchantApplyStatusEnum.FINISHED:
        //   router.push({
        //     name: ROUTE_NAME.AUDIT_RESULTS,
        //     query: {
        //       ...route.query,
        //       applyId: res.id,
        //       fullscreen: 'true',
        //       shopBizApplyType: customClaimType,
        //     },
        //     params: { ...route.params },
        //   })
        //   return
        // // 待提交
        // case MerchantApplyStatusEnum.TO_SUBMIT:
        //   router.push({
        //     name: ROUTE_NAME.BUSINESS_LICENSE_STORE_MANAGE,
        //     query: {
        //       ...route.query,
        //       fullscreen: 'true',
        //     },
        //     params: { ...route.params },
        //   })
        //   return
        // // 待社区审核 | 待bd审核 | 待服务商审核 | 审核拒绝 | 用户取消申请 | 用户撤回申请
        // case MerchantApplyStatusEnum.TO_NEW_BRAND_AUDIT:
        // case MerchantApplyStatusEnum.TO_BD_AUDIT:
        // case MerchantApplyStatusEnum.TO_SP_AUDIT:
        // case MerchantApplyStatusEnum.AUDIT_REJECT:
        // case MerchantApplyStatusEnum.CANCEL:
        // case MerchantApplyStatusEnum.WITHDRAW:
        //   router.push({
        //     name: ROUTE_NAME.AUDIT_RESULTS,
        //     query: {
        //       ...route.query,
        //       applyId: res.id,
        //       fullscreen: 'true',
        //     },
        //     params: { ...route.params },
        //   })
        //   return
        // // 待支付页面
        // case MerchantApplyStatusEnum.TO_PAY:
        //   return
        default:
          break
      }
    } catch (error) {
      handleError(error, '入驻进度查询')
    }
  }

  // 共用：判断当前 POI 是否已被认领
  const postApiRedlifeAPi = async () => {
    if (!claimStorePageFormData.value.poiId) return Promise.resolve()

    try {
      const params = { poiId: claimStorePageFormData.value.poiId }
      const res = await postApiRedlife(params)
      if (res.isClaimed) {
        const serviceLink = await jumpContactXhsSupport(false)
        uploadAlertData({
          type: AlertTypeEnum.POI_CLAIMED,
          message: '该地点已被认领，无法重复认领，如有疑问请联系客服',
          confirmText: '联系客服',
          showCancelButton: false,
          link: serviceLink
        })
      }
    } catch (error) {
      handleError(error, 'POI认领状态检查')
    }
  }

  // 入驻：认领回显信息查询
  const getDraftQueryApi = async () => {
    if ((isEdit && !shopApplyState.value.id)) return

    try {
      const params = { applyId: shopApplyState.value.id }
      const res = await getDraftQuery(params)
      store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', res)
    } catch (error) {
      handleError(error, '草稿信息查询')
    }
  }

  // 轻、本地认领：认领回显信息查询
  const postQueryClaimInfoApi = async () => {
    try {
      const params: Record<string, any> = {}
      // 这里存在applyId是从列表里点进去的，这期没有，所以可以携带参数。
      if (+shopBizApplyType === PoiClaimApplyType.PRO_CLAIM_APPLY) {
        params.sourceFrom = 'PRO_USER'
        if (applyId) params.applyId = applyId
        store.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', ClaimTypeEnum.LIGHT_CLAIM)
      } else if (shopId) {
        params.shopId = shopId
        store.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', ClaimTypeEnum.LOCAL_CLAIM)
      }

      // 判断params是否为空对象
      if (Object.keys(params).length === 0) return
      const res = await postQueryClaimInfo(params)
      if (res?.poiClaimApplyInfo?.poiClaimDetailInfo) {
        store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', res.poiClaimApplyInfo.poiClaimDetailInfo)
      }
    } catch (error) {
      handleError(error, '草稿信息查询')
    }
  }

  // 入驻检查，存储真正的认证类型
  const frontGetCanClaimPoiMerchantApi = async () => {
    if (!claimStorePageFormData.value.xhsCategoryId) return
    try {
      const params = {
        xhsCategoryId: claimStorePageFormData.value.xhsCategoryId
      }
      const res = await getCanClaimPoiMerchant(params)

      // 当canMerchant不存在 && 前置接口返回入驻走入驻，剩余根据前置接口走
      const newClaimType = (!res?.canMerchant && userInfo.value.claimType === ClaimTypeEnum.MERCHANT_ENTER)
        ? ClaimTypeEnum.LIGHT_CLAIM
        : userInfo.value.claimType

      store.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', newClaimType)
    } catch (error) {
      handleError(error, '信息保存')
    }
  }

  // 数据初始化函数
  async function init() {
    const dataSource = getDataSource()

    /** *
     *
     *  getPreCheckApi(), 预览前置接口
     *  getApplyQueryApi（） 查询入驻总进度
     *  frontGetCanClaimPoiMerchantApi(),入驻获取真正的类型
     *  getDraftQueryApi（） 入驻认领门店信息接口 || postQueryClaimInfoApi() 轻、本地认领门店信息接口

     *
     *  postApiRedlifeAPi（） POI是否被认领
     *  getCanClaimPoiMerchant（）。下一步时校验接口，判断是否可以入驻
     * * */

    // 认领门店进入一定是本地，区分 有没有applyID，有applyId从列表进入可以区分本地，剩下全是独立入驻。

    try {
      // 前置校验接口
      loading.value = true

      await getPreCheckApi()

      // 独立入驻进度接口,一开始的前置接口能判断他现在是什么类型，只有poi选择完成后才知道他到底是不是能入驻。
      if (userInfo.value.claimType === ClaimTypeEnum.MERCHANT_ENTER) {
        await getApplyQueryApi()
      }

      // 需要调用API就调用
      if (dataSource.needsApiCall) {
        const api = store.state.ShopUserInfoStore.customClaimType === ClaimTypeEnum.MERCHANT_ENTER || isEdit ? getDraftQueryApi : postQueryClaimInfoApi
        await api()
      }

      // 查询当前认领真正的类型
      await frontGetCanClaimPoiMerchantApi()

      // 判断POI是否被认领
      await postApiRedlifeAPi()

      loading.value = false

      // API完成后才初始化表单
      fillFormModelFromSource(formModel, store.state.ClaimShopStore.claimStorePageFormData)
    } catch (error) {
      handleError(error, '数据初始化')
    }
  }

  // TODO:新增一个弹窗
  //

  onMounted(() => {
    init()
  })

  return {
    // 变量
    formItems,
    formModel,
    FormItemTypeEnum,
    alertShow,
    alertTitle,
    message,
    confirmText,
    showCancelButton,
    loading,

    // 计算属性
    isSubmitDisabled,

    // 方法
    poiShopInfoTip,
    formItemClick,
    nextStep,
    confirmAlert,
    cancelAlert
  }
}
