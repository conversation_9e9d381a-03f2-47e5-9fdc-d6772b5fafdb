<template>
  <div class="claim-legal-person-page">
    <header>
      <Header>
        <template #right>
          <div class="claim-shop-header" @click="handleCustomerServiceClick">
            <Icon icon-name="StoreChat" size="16"></Icon>
            <Text class="customer">客服</Text>
          </div>
        </template>
      </Header>
    </header>
    <Steps type="CLAIM_SHOP" />
    <main>
      <LanguageProvider>
        <LegalPersonForm
          ref="legalPersonFormRef"
          :form-data="formData"
          :form-rules="formRules"
          :is-id-parsed="isIdParsed"
          :is-settle="false"
          :is-preview="isPreview"
          :handle-info-extracted="handleInfoExtracted"
          @focus="handleFocus"
          @submitSuccess="handleSubmit"
          @submitError="handleValidationError"
        />
      </LanguageProvider>
    </main>

    <footer>
      <Button
        size="large"
        :style="{width: '173px'}"
        @click="handlePrevious"
      >
        上一步
      </Button>
      <Button
        type="primary"
        size="large"
        :style="{width: '173px'}"
        :variant="isSubmitDisabled ? 'disabled' : 'fill'"
        :disabled="isSubmitDisabled || isSubmitting"
        @click="handleFormValidation"
      >
        {{ submitButtonText }}
      </Button>
    </footer>
  </div>
</template>

<script setup lang="ts">
  import {
    Button,
    LanguageProvider,
    Icon,
    Text
  } from '@xhs/reds-h5-next'

  // 组件
  import Header from '~/components/header/index.vue'
  import LegalPersonForm from '~/components/LegalPersonForm/index.vue'
  import Steps from '~/components/Steps/index.vue'

  // composable
  import { useClaimLegalPersonForm } from './composables/useClaimLegalPersonForm'

  // 方法
  import { jumpContactXhsSupport } from '~/utils/shopPage'

  const {
    legalPersonFormRef,
    // 数据
    formData,
    formRules,
    isPreview,
    isIdParsed,

    // 计算属性
    isSubmitDisabled,
    submitButtonText,
    isSubmitting, // 添加提交loading状态

    // 方法
    handleFocus,
    handlePrevious,
    handleInfoExtracted,
    handleFormValidation,
    handleValidationError,
    handleSubmit,
  } = useClaimLegalPersonForm()

  // 客服点击处理
  const handleCustomerServiceClick = () => {
    jumpContactXhsSupport()
  }
</script>

<style lang="stylus" scoped>
// 页面布局
.claim-legal-person-page
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5
  padding-top 24px
  // iOS 安全区域适配 - 顶部
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

  header
    flex 0 0 auto
    .customer
      color rgba(0, 0, 0, 0.8)

    .claim-shop-header
      display flex
      align-items center
      justify-content center

  main
    flex 1 1 0
    min-height 0
    padding 0 16px
    margin-top 16px
    overflow auto
    -webkit-overflow-scrolling touch

  footer
    display flex
    align-items center
    justify-content space-between
    flex-shrink 0
    min-height 50px
    gap 15.5px
    padding 8px 16px
    // iOS 安全区域适配 - 底部
    @supports (padding-bottom: constant(safe-area-inset-bottom))
      padding-bottom constant(safe-area-inset-bottom)
    @supports (padding-bottom: env(safe-area-inset-bottom))
      padding-bottom env(safe-area-inset-bottom)
</style>
