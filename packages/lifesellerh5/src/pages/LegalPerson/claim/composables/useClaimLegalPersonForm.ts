import {
  computed, ComputedRef,
  onMounted,
  Ref,
  ref
} from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from '@xhs/reds-h5-next'
import { useBaseLegalPersonForm, BaseLegalPersonFormReturn, LegalPersonData } from '~/components/LegalPersonForm/useBaseLegalPersonForm'
import { transformLegalPersonData, transformApiToFormData } from '~/utils/legalPersonTransform'
import { postUpsertClaimPoi } from '~/services/edith_post_upsert_claim_poi'
import { getQueryLegalPersonQual } from '~/services/edith_get_query_legal_person_qual'
import ROUTE_NAME from '~/constants/routename'
import { PoiClaimApplyType } from '~/enum/shop'

interface LegalPersonFormInstance {
  validateForm: () => Promise<boolean>
  formRef: any
}

// 认领模块特定的返回类型
interface UseClaimLegalPersonFormReturn extends BaseLegalPersonFormReturn {
  legalPersonFormRef: Ref<LegalPersonFormInstance | null>
  isSubmitDisabled: ComputedRef<boolean>
  submitButtonText: ComputedRef<string>
  isSubmitting: Ref<boolean>
  handleFocus: (name: string) => void
  handleSubmit: () => Promise<void>
  handlePrevious: () => void
  handleFormValidation: () => Promise<void>
  handleValidationError: (error: any) => void
}

export function useClaimLegalPersonForm(): UseClaimLegalPersonFormReturn {
  const router = useRouter()
  const route = useRoute()
  const store = useStore()

  // 判断是否为编辑模式 - 基于是否有shopId或poiId参数
  const isEdit = computed(() => {
    const claimStoreData = store.state.ClaimShopStore?.claimStorePageFormData
    const { id, shopId } = claimStoreData || {}
    return !!(id || shopId)
  })

  const legalPersonFormRef = ref<LegalPersonFormInstance | null>(null)
  const baseForm = useBaseLegalPersonForm()

  // 添加提交loading状态
  const isSubmitting = ref(false)

  // 计算提交按钮是否禁用
  const isSubmitDisabled = computed(() => {
    const requiredFields = ['validityPeriod', 'idNumber', 'name']

    // 检查必填字段
    const hasEmptyField = requiredFields.some(field => {
      const value = baseForm.formData.value[field]
      return !value || (typeof value === 'string' && value.trim() === '')
    })

    // 检查证件图片
    const idImages = baseForm.formData.value.idImages || []
    const frontImageValid = idImages[0] && (idImages[0].url || idImages[0].file)
    const backImageValid = baseForm.needsBackImage.value
      ? (idImages[1] && (idImages[1].url || idImages[1].file))
      : true

    return hasEmptyField || !frontImageValid || !backImageValid
  })

  // 重写handleInfoExtracted，处理入驻特有的OCR字段
  const handleInfoExtracted = (info: any) => {
    // 调用基础方法处理通用字段
    baseForm.handleInfoExtracted(info)

    // console.log(baseForm.isIdParsed.value, 'isIdParsed.value')
    baseForm.isIdParsed.value = true
  }

  // 计算提交按钮文本
  const submitButtonText = computed(() => '提交审核')

  // 表单校验方法
  const handleFormValidation = async () => {
    if (!legalPersonFormRef.value) return
    await legalPersonFormRef.value.validateForm()
  }

  // 处理校验错误
  const handleValidationError = (error: any) => {
    // console.error('表单校验失败:', error)
    showToast({
      message: error?.message || '请检查表单信息',
      duration: 2000
    })
  }

  const handleFocus = (name: string) => {
    if (isEdit.value) {
      baseForm.formData.value[name] = ''
    }
  }

  // 处理提交 - 认领模块将数据存储到store
  const handleSubmit = async () => {
    if (isSubmitDisabled.value) {
      showToast({
        message: '请完善必填信息',
        duration: 2000
      })
      return
    }

    // console.log('认领模块法人表单提交:', baseForm.formData.value)

    isSubmitting.value = true // 开始提交loading

    try {
      // console.log('表单验证通过，准备提交数据:', baseForm.formData.value)
      const legalPersonQualData = transformLegalPersonData(baseForm?.formData?.value as LegalPersonData)

      // 将数据存储到 Vuex store
      // await store.dispatch('claimStore/updateLegalPersonInfo', apiData)
      const newValue = {
        ...store.state.ClaimShopStore.claimStorePageFormData,
        poiLegalPersonQual: legalPersonQualData
      }
      store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', newValue)

      const {
        id,
        poiId,
        poiName,
        gaodeId,
        poiAddress,
        businessLicenseType,
        businessLicenseImage,
        usci,
        companyName,
        businessAddress,
        businessLicenseValidity,
        qualificationMap,
        // 从store中获取更多需要的字段
        shopId,
        longitude,
        latitude,
        provinceName,
        cityName,
        districtName,
        addressDetail,
        categoryId,
        sourceFrom,
        sourceIdentity,
        unchangedSecureFields,
        useProfessionalQualification
      } = store.state.ClaimShopStore.claimStorePageFormData ?? {}

      // 构建完整的API请求payload
      const payload = {
        // 基础信息
        id,
        poiId, // 兼容不同字段名
        shopId,
        gaodeId,
        poiName,

        // 地址相关信息
        longitude,
        latitude,
        provinceName,
        cityName,
        districtName,
        addressDetail: addressDetail || poiAddress, // 兼容不同字段名

        // 营业执照相关信息
        businessLicenseType,
        businessLicenseImage,
        usci,
        companyName,
        businessAddress,
        businessLicenseValidity,
        // 资质信息
        qualificationMap: qualificationMap || {},
        // 法人资质信息
        poiLegalPersonQual: legalPersonQualData,

        // 其他信息
        categoryId,
        sourceFrom,
        sourceIdentity,
        unchangedSecureFields,
        isUseProCertification: useProfessionalQualification
      }

      console.log('完整的API请求数据:', payload)

      // 调用API
      const response = await postUpsertClaimPoi(payload)
      console.log('API响应结果:', response)

      // 跳转到下一步或返回
      router.replace({
        name: ROUTE_NAME.AUDIT_RESULTS, // 或其他适当的页面
        query: {
          ...route.query,
          shopBizApplyType: PoiClaimApplyType.POI_CLAIM_APPLY,
          applyId: response.applyId,
          shopId,
        }
      })
    } catch (error) {
      // console.error('认领模块法人信息保存失败:', error)
      showToast({
        message: '保存失败，请重试',
        duration: 2000
      })
    } finally {
      isSubmitting.value = false // 结束提交loading
    }
  }

  // 处理返回上一步
  const handlePrevious = () => {
    router.go(-1)
  }

  // 预览/编辑 初始化数据
  const init = async () => {
    // 预览模式或编辑模式都需要初始化数据
    if (!baseForm.isPreview.value && !isEdit.value) return

    try {
      // 从 store 中获取数据
      const claimStoreData = store.state.ClaimShopStore?.claimStorePageFormData
      const { poiId, shopId, poiLegalPersonQual } = claimStoreData || {}

      if (!poiId && !shopId) {
        console.warn('缺少poiId或shopId参数，无法查询法人资质')
        return
      }

      let legalPersonData: any = null

      // 优先使用store中的法人数据（用户可能已经修改过）
      if (poiLegalPersonQual && (
        poiLegalPersonQual.name
        || poiLegalPersonQual.idNumber
        || poiLegalPersonQual.idFrontSide?.url
        || poiLegalPersonQual.idBackSide?.url
      )) {
        // console.log('使用store中已有的法人数据')
        legalPersonData = poiLegalPersonQual
      } else {
        // 如果store中没有法人数据，则从接口获取
        // console.log('store中无法人数据，从接口获取')
        const response = await getQueryLegalPersonQual({
          poiId,
          shopId
        })
        legalPersonData = response?.poiLegalPersonQual
      }

      if (legalPersonData) {
        // 使用 transformApiToFormData 来转换数据
        const formData = transformApiToFormData(legalPersonData)

        // 基础信息
        if (formData.name) baseForm.formData.value.name = formData.name
        if (formData.idType) baseForm.formData.value.idType = formData.idType
        if (formData.idNumber) baseForm.formData.value.idNumber = formData.idNumber
        if (formData.phoneNumber) baseForm.formData.value.phoneNumber = formData.phoneNumber

        // 处理证件图片
        if (formData.idImages) {
          baseForm.formData.value.idImages = formData.idImages
        }

        // 处理证件有效期
        if (formData.validityPeriod) {
          baseForm.formData.value.validityPeriod = formData.validityPeriod
        }

        // 更新身份证解析状态
        baseForm.isIdParsed.value = true
      }
    } catch (error) {
      console.error('获取法人信息失败:', error)
      // showToast({
      //   message: '获取法人信息失败',
      //   duration: 2000
      // })
    }
  }

  onMounted(() => {
    init()
  })

  return {
    legalPersonFormRef,
    // 继承基础功能
    ...baseForm,

    // 认领模块特有的计算属性
    isSubmitDisabled,
    submitButtonText,
    isSubmitting,

    // 认领模块特有的方法
    handleFocus,
    handleSubmit,
    handleInfoExtracted,
    handlePrevious,
    handleFormValidation,
    handleValidationError,
  }
}
