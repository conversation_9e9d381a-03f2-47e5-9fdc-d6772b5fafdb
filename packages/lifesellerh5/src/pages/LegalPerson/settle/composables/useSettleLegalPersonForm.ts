import {
  computed, ComputedRef, onMounted, onUnmounted, Ref, ref
} from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import { showToast, ToastType } from '@xhs/reds-h5-next'
import { invokeInterceptorWithoutValidate } from '@xhs/ozone-schema/src/lib/invoke'
import { cloneDeep } from 'lodash'

import {
  useBaseLegalPersonForm,
  BaseLegalPersonFormReturn,
  BaseLegalPersonData,
} from '~/components/LegalPersonForm/useBaseLegalPersonForm'
import { postApplySubmit } from '~/services/edith_post_apply_submit'

// 发送验证码相关
import { postVerificationcodeSend } from '~/services/edith_post_verificationcode_send'
import { postVerificationcodeCheck } from '~/services/edith_post_verificationcode_check'

import { getTwoelementCheck } from '~/services/edith_get_twoelement_check'

// 人脸识别相关
import { postRedlifeFacekey } from '~/services/edith_post_redlife_facekey'
import { postFaceResult } from '~/services/edith_post_face_result'
import { postFaceApply } from '~/services/edith_post_face_apply'

// 查询法人信息
import { postAuthorizationQuery } from '~/services/edith_post_authorization_query'
import { IMerchantAuthorizationDraft, postAuthorizationSave } from '~/services/edith_post_authorization_save'
import ROUTE_NAME from '~/constants/routename'
import { postSubjectQuery } from '~/services/edith_post_subject_query'

// 验证方式枚举
export enum VerifyMethod {
  PHONE = 'phone',
  FACE = 'face',
}
// 验证方式选项
const VERIFY_METHOD_OPTIONS = [
  { value: VerifyMethod.PHONE, label: '手机号' },
  { value: VerifyMethod.FACE, label: '人脸认证' },
]

interface LegalPersonFormInstance {
  validateForm: () => Promise<boolean>
  formRef: any
}

// 入驻模块特定的返回类型
interface UseSettleLegalPersonFormReturn extends BaseLegalPersonFormReturn {
  legalPersonFormRef: Ref<LegalPersonFormInstance | null>
  isSubmitDisabled: ComputedRef<boolean>
  verifyMethodPickerColumns: ComputedRef<any[]>
  submitButtonText: ComputedRef<string>
  showSendButton: ComputedRef<boolean>
  canSendCode: ComputedRef<boolean | string>
  codePlaceholder: ComputedRef<string>
  sendCodeButtonText: ComputedRef<string>
  verifyMethodSheetVisible: Ref<boolean>
  verifyMethodText: ComputedRef<string>
  verifyMethodPickerValue: ComputedRef<string[]>

  // 添加loading状态
  isSubmitting: Ref<boolean>
  isInitializing: Ref<boolean>
  isSendingCode: Ref<boolean>

  confirmVerifyMethodPicker: (values: string[]) => void
  sendVerifyCode: () => Promise<void>
  validateVerifyCode: (phone?: string, code?: string) => Promise<{ success: boolean; errorMsg?: string }>
  showVerifyMethodSheet: () => void
  hideVerifyMethodSheet: () => void
  handleAgreementClick: (index: number) => void
  handleFormValidation: () => Promise<void>
  handleValidationError: (error: Error) => void
  handleSubmit: () => Promise<void>
  handlePrevious: () => void
}

interface SettleLegalPersonData extends BaseLegalPersonData {
  verifyMethod: VerifyMethod
  phone: string
  verifyCode: string
  agreementAccepted: boolean
  faceKey?: string // 人脸识别的key
  faceIdentificationOrder?: string // 人脸识别订单号

  // OCR解析相关字段
  certificateAddress?: string // 证件地址 (从OCR解析)
  issuingAuthority?: string // 签发机关 (从OCR解析)
  fileAttachmentList?: any[] // 文件附件列表 (从上传组件传递)
}

export function useSettleLegalPersonForm(verifyAlertRef: Ref<any>): UseSettleLegalPersonFormReturn {
  const router = useRouter()
  const route = useRoute()
  const store = useStore()

  const shopApplyState = computed(() =>
    store.state.ShopApplyStore.shopApplyState)
  const isEdit = computed(() => !!shopApplyState.value.id)

  let visibilityChangeHandler: (() => void) | null = null

  const legalPersonFormRef = ref<LegalPersonFormInstance | null>(null)
  const baseForm = useBaseLegalPersonForm<SettleLegalPersonData>({
    initialData: {
      verifyMethod: VerifyMethod.PHONE, // 默认手机号验证
      phone: '',
      verifyCode: '',
      agreementAccepted: false,
    },
  })

  // 添加各种loading状态
  const isSubmitting = ref(false)
  const isInitializing = ref(false)
  const isSendingCode = ref(false) // 发送验证码loading状态

  // 重写handleInfoExtracted，处理入驻特有的OCR字段
  const handleInfoExtracted = (info: any) => {
    // 调用基础方法处理通用字段
    baseForm.handleInfoExtracted(info)

    // 处理入驻特有的OCR字段
    if (info.address) {
      formData.value.certificateAddress = info.address
    }
    if (info.issuingAuthority) {
      formData.value.issuingAuthority = info.issuingAuthority
    }
    if (info.fileAttachmentList) {
      formData.value.fileAttachmentList = info.fileAttachmentList
    }
    // console.log(baseForm.isIdParsed.value, 'isIdParsed.value')
    baseForm.isIdParsed.value = true
  }
  // 验证码相关
  const codeCountdown = ref(0)
  const codeTimer = ref<NodeJS.Timeout | null>(null)
  const verifyMethodSheetVisible = ref(false)
  const hasSentCode = ref(false) // 记录是否已发送过验证码

  const { formData } = baseForm

  // 入驻模块的额外表单验证规则
  const additionalRules = computed(() => ({
    ...baseForm.formRules.value,
    phone: [
      {
        type: 'string',
        required: formData.value.verifyMethod === VerifyMethod.PHONE,
        validator: (_rule: any, val: string) => {
          if (formData.value.verifyMethod !== VerifyMethod.PHONE) return true
          return (
            val !== undefined && val !== null && val.trim() !== '' && /^1[3-9]\d{9}$/.test(val)
          )
        },
        message: '请输入正确的手机号',
      },
    ],
    verifyCode: [
      {
        required: formData.value.verifyMethod === VerifyMethod.PHONE,
        validator: (_rule: any, val: string) => {
          if (formData.value.verifyMethod !== VerifyMethod.PHONE) return true
          return val !== undefined && val !== null && val.trim() !== ''
        },
        message: '请输入验证码',
      },
    ],
    agreementAccepted: [
      {
        required: true,
        validator: (_rule: any, val: boolean) => val === true,
        message: '请同意服务协议',
      },
    ],
  }))

  // 覆盖基础表单规则
  const formRules = computed(() => additionalRules.value)

  const verifyMethodPickerColumns = computed(() => [
    VERIFY_METHOD_OPTIONS.map(option => ({
      label: option.label,
      value: option.value,
    })),
  ])

  // 按钮是否显示：倒计时期间和已输入验证码时隐藏，其他时候都显示
  const showSendButton = computed(() => codeCountdown.value === 0 && !formData.value.verifyCode)

  // 按钮是否可点击：手机号格式正确且不在发送状态时才可点击
  const canSendCode = computed(
    () => formData.value.phone && /^1[3-9]\d{9}$/.test(formData.value.phone) && !isSendingCode.value
  )

  const codePlaceholder = computed(() => {
    if (codeCountdown.value > 0) {
      return `重新发送（${codeCountdown.value}s）`
    }
    return ''
  })

  // 发送验证码按钮文字
  const sendCodeButtonText = computed(() => {
    if (isSendingCode.value) {
      return '发送中...'
    }
    return hasSentCode.value ? '重新发送' : '发送验证码'
  })

  const verifyMethodPickerValue = computed(() => [formData.value.verifyMethod])

  const verifyMethodText = computed(() => {
    const option = VERIFY_METHOD_OPTIONS.find(item => item.value === formData.value.verifyMethod)
    return option?.label || ''
  })

  // 人脸识别轮询定时器
  const facePollingTimer = ref<NodeJS.Timeout | null>(null)
  // 是否在轮询人脸识别结果
  const isFacePolling = ref(false)

  const startFaceRecognition = async () => {
    try {
      // 1. 身份证二要素检测
      const twoElementResult = await getTwoelementCheck({
        name: formData.value.name,
        idNumber: formData.value.idNumber,
      })

      if (!twoElementResult.verifyPass) {
        showToast({
          type: ToastType.ToastBuiltInType.TEXT,
          message: '身份证信息验证失败，请检查姓名和身份证号',
          duration: 2000,
        })
        return false
      }

      // 2. 生成faceKey
      const { faceKey } = await postRedlifeFacekey({
        name: formData.value.name,
        idNumber: formData.value.idNumber,
      })
      if (!faceKey) {
        showToast({
          type: ToastType.ToastBuiltInType.TEXT,
          message: '生成人脸识别密钥失败',
          duration: 2000,
        })
        return false
      }
      formData.value.faceKey = faceKey

      // 3. 申请人脸识别，获取 deeplink
      const res = await postFaceApply({
        key: faceKey,
        platform: 'XHS',
      })

      if (!res.faceToken || !res.faceToken.deeplink) {
        showToast({
          type: ToastType.ToastBuiltInType.TEXT,
          message: '获取人脸识别链接失败',
          duration: 2000,
        })
        return false
      }

      const deeplink = res.faceToken?.deeplink
      // 保存人脸识别订单号
      formData.value.faceIdentificationOrder = res.faceToken?.identityToken

      // 4. 开始轮询人脸识别结果
      startFacePolling()

      // 5. 跳转到人脸识别页面
      invokeInterceptorWithoutValidate('replaceSelfWithLink', {
        type: 'present',
        link: deeplink,
      }).catch(() => {
        // 降级 location 打开
        window.location.href = deeplink
      })

      return true
    } catch (error) {
      // console.error('人脸识别启动失败:', error)
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '人脸识别启动失败，请重试',
        duration: 2000,
      })
      return false
    }
  }

  // 开始轮询人脸识别结果
  const startFacePolling = () => {
    if (isFacePolling.value) return

    isFacePolling.value = true

    const pollFaceResult = async () => {
      // 如果faceKey不存在，停止轮询
      if (!formData.value.faceKey) {
        stopFacePolling()
        return
      }

      try {
        const { faceVerifyResult } = await postFaceResult({
          key: formData.value.faceKey,
        })

        if (faceVerifyResult?.authStatus === 'success') {
          // 人脸识别成功
          stopFacePolling()
          showToast({
            type: ToastType.ToastBuiltInType.TEXT,
            message: '人脸识别成功',
            duration: 2000,
          })
          // 保持人脸验证方式
          return
        }
        if (faceVerifyResult?.authStatus === 'fail') {
          // 人脸识别失败
          stopFacePolling()
          // 切换回手机号验证方式
          formData.value.verifyMethod = VerifyMethod.PHONE
          formData.value.faceKey = undefined
          formData.value.faceIdentificationOrder = undefined
          showToast({
            type: ToastType.ToastBuiltInType.TEXT,
            message: faceVerifyResult.failMsg || '人脸识别失败，请重试',
            duration: 2000,
          })
          return
        }
        // 继续轮询（状态为processing或其他中间状态）
      } catch (error) {
        console.error('查询人脸识别结果失败:', error)
      }
    }

    // // 立即执行一次
    // pollFaceResult()

    // 设置3秒间隔轮询
    facePollingTimer.value = setInterval(pollFaceResult, 3000)
  }

  // 停止轮询人脸识别结果
  const stopFacePolling = () => {
    if (facePollingTimer.value) {
      clearInterval(facePollingTimer.value)
      facePollingTimer.value = null
    }
    isFacePolling.value = false
  }

  const handleVisibilityChange = () => {
    if (document.visibilityState === 'visible' && !isFacePolling.value && formData.value.faceKey) {
      // 页面重新可见且没有在轮询时，重新开始轮询
      startFacePolling()
    } else if (document.visibilityState === 'hidden' && isFacePolling.value) {
      // 页面隐藏时停止轮询（节省资源）
      stopFacePolling()
    }
  }

  // Picker确认选择验证方式
  const confirmVerifyMethodPicker = async (values: string[]) => {
    if (values.length > 0) {
      const newMethod = values[0]

      // 如果选择了人脸验证方式
      if (newMethod === VerifyMethod.FACE) {
        // 检查必填信息是否完整
        if (!formData.value.name || !formData.value.idNumber) {
          showToast({
            type: ToastType.ToastBuiltInType.TEXT,
            message: '请先填写完整的姓名和身份证号',
            duration: 2000,
          })
          hideVerifyMethodSheet()
          return
        }

        // 在跳转前添加页面可见性监听
        if (!visibilityChangeHandler) {
          visibilityChangeHandler = handleVisibilityChange
          document.addEventListener('visibilitychange', visibilityChangeHandler)
        }

        // 执行完整的人脸识别流程
        const success = await startFaceRecognition()
        if (success) {
          // 清空手机号相关信息
          formData.value.phone = ''
          formData.value.verifyCode = ''
          hasSentCode.value = false
          stopCountdown()
          formData.value.verifyMethod = newMethod as VerifyMethod
        }
      } else {
        // 如果切换到其他验证方式，停止人脸识别轮询
        stopFacePolling()
        formData.value.faceKey = undefined
        formData.value.faceIdentificationOrder = undefined

        // 如果验证方式改变，清空手机号和验证码
        if (formData.value.verifyMethod !== newMethod) {
          formData.value.phone = ''
          formData.value.verifyCode = ''
          // 重置发送状态
          hasSentCode.value = false
          // 停止验证码倒计时
          stopCountdown()
        }
        formData.value.verifyMethod = newMethod as VerifyMethod
      }
    }
    hideVerifyMethodSheet()
  }

  // 校验验证码
  const validateVerifyCode = async (phone?: string, code?: string): Promise<{ success: boolean; errorMsg?: string }> => {
    const phoneNumber = phone || formData.value.phone
    const verifyCode = code || formData.value.verifyCode

    if (!phoneNumber || !verifyCode) {
      return { success: false, errorMsg: '手机号和验证码不能为空' }
    }

    try {
      // 使用 transform: false 以获取完整的原始响应
      const response = await postVerificationcodeCheck({
        verificationCodeInfo: {
          verificationType: 'phone',
          contactInfo: phoneNumber,
          verificationCode: verifyCode,
          zoneCode: '86',
        },
      }, { transform: false }) as any

      // 根据接口返回格式判断：
      // 2. 检查 data.checkResult.checkSuccess
      const checkSuccess = response?.checkResult?.checkSuccess === true
      const errorMsg = response?.checkResult?.errorMsg || ''
      return {
        success: checkSuccess,
        errorMsg: checkSuccess ? undefined : (errorMsg || '验证码校验失败')
      }
    } catch (error: any) {
      return { success: false, errorMsg: '网络错误，请重试' }
    }
  }

  // 发送验证码
  const sendVerifyCode = async () => {
    // 检查手机号是否输入
    if (!formData.value.phone) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '请先输入手机号',
        duration: 2000,
      })
      return
    }

    // 检查手机号格式
    if (!/^1[3-9]\d{9}$/.test(formData.value.phone)) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '请输入正确的手机号',
        duration: 2000,
      })
      return
    }

    // 如果正在发送中，直接返回
    if (isSendingCode.value) {
      return
    }

    isSendingCode.value = true // 开始发送loading

    try {
      await postVerificationcodeSend({
        verificationCodeInfo: {
          zoneCode: '86',
          verificationType: 'phone',
          contactInfo: formData.value.phone,
        },
      })

      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '验证码已发送',
        duration: 2000,
      })

      // 标记已发送过验证码
      hasSentCode.value = true

      // 开始倒计时
      startCountdown()
    } catch (error) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '发送验证码失败，请重试',
        duration: 2000,
      })
    } finally {
      isSendingCode.value = false // 结束发送loading
    }
  }

  // 开始倒计时
  const startCountdown = () => {
    codeCountdown.value = 60
    codeTimer.value = setInterval(() => {
      codeCountdown.value -= 1
      if (codeCountdown.value <= 0) {
        clearInterval(codeTimer.value as NodeJS.Timeout)
        codeTimer.value = null
      }
    }, 1000)
  }

  // 停止倒计时
  const stopCountdown = () => {
    if (codeTimer.value) {
      clearInterval(codeTimer.value)
      codeTimer.value = null
      codeCountdown.value = 0
    }
  }

  // 验证方式相关方法
  const showVerifyMethodSheet = () => {
    if (!formData.value.idNumber) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '请先填写身份证号码',
        duration: 2000,
      })
      return
    }
    verifyMethodSheetVisible.value = true
  }

  const hideVerifyMethodSheet = () => {
    verifyMethodSheetVisible.value = false
  }

  // 计算提交按钮是否禁用
  const isSubmitDisabled = computed(() => {
    const requiredFields = ['idNumber', 'verifyMethod']

    // 检查证件图片
    const frontImageValid = formData.value.idImages[0]
    const backImageValid = baseForm.needsBackImage.value ? formData.value.idImages[1] : true

    // 检查验证方式相关字段
    let verificationValid = false
    if (formData.value.verifyMethod === VerifyMethod.FACE) {
      // 人脸识别模式：需要有人脸识别订单号
      verificationValid = !!formData.value.faceIdentificationOrder
    } else if (formData.value.verifyMethod === VerifyMethod.PHONE) {
      // 手机号验证模式：需要手机号和验证码
      verificationValid = !!(formData.value.phone && formData.value.verifyCode)
    }

    // 检查协议同意
    const agreementValid = formData.value.agreementAccepted === true

    return (
      requiredFields.some(field => !formData.value[field])
      || !frontImageValid
      || !backImageValid
      || !verificationValid
      || !agreementValid
    )
  })

  // 计算提交按钮文本
  const submitButtonText = computed(() => '提交审核')

  const handleFormValidation = async () => {
    if (!legalPersonFormRef.value) return
    await legalPersonFormRef.value.validateForm()
  }
  // 表单验证失败
  const handleValidationError = (error: Error) => {
    showToast({
      message: error.message || '表单验证失败',
      duration: 2000
    })
  }

  const handleVerify = (verifyAlertRef: Ref<any>) => {
    // 调用校验方法，传入申请ID和回调函数
    verifyAlertRef.value?.verify({
      onConfirm: async () => {
        try {
          // 提交申请
          await postApplySubmit()
          router.push({
            name: ROUTE_NAME.AUDIT_RESULTS_SETTLE, // 替换为实际的下一步页面
            query: {
              ...route.query,
            },
          })
        } catch (error) {
          showToast({
            type: ToastType.ToastBuiltInType.ERROR,
            message: '提交失败',
            duration: 2000,
          })
        }
      },
      onCancel: data => {
        console.log('用户取消操作', data)
        // 处理取消逻辑，data 可能为 null
      },
      onModify: data => {
        console.log('用户选择修改信息', data)
        // 跳转专业号中心
      }
    })
  }

  // 处理提交 - 入驻模块直接调用API
  const handleSubmit = async () => {
    if (isSubmitDisabled.value) return

    isSubmitting.value = true // 开始提交loading

    try {
      // 二要素校验
      const { verifyPass } = await getTwoelementCheck({
        idNumber: formData.value.idNumber,
        name: formData.value.name,
      })
      if (!verifyPass) {
        showToast({
          type: ToastType.ToastBuiltInType.TEXT,
          message: '二要素校验失败，请检查身份证号和姓名',
          duration: 2000,
        })
        return
      }

      // 根据验证方式进行相应的校验
      if (formData.value.verifyMethod === VerifyMethod.PHONE) {
        // 手机号验证模式：校验验证码
        const verifyResult = await validateVerifyCode()
        if (!verifyResult.success) {
          throw new Error(verifyResult.errorMsg || '验证码校验失败，请检查验证码是否正确')
        }
      } else if (formData.value.verifyMethod === VerifyMethod.FACE) {
        // 人脸识别模式：检查人脸识别订单号是否存在
        if (!formData.value.faceIdentificationOrder) {
          throw new Error('人脸识别未完成，请重新进行人脸识别')
        }
      }

      // 构建文件附件列表
      const buildFileAttachmentList = () => formData.value.idImages
        .filter(image => image?.url) // 过滤掉空的图片
        .map((image, index) => ({
          qualificationCode: index === 0 ? '889821130321592320' : '889821308189442048',
          qualificationName: `身份证${index === 0 ? '人像面' : '国徽面'}`,
          fileAttachmentList: [{
            fileName: image.fileName,
            fileType: image.fileType,
            height: image.height,
            width: image.width,
            uploaderInfoModel: {
              bizName: image.bizName,
              scene: image.scene,
              fileId: image.fileId,
              url: image.url,
              isSecret: image.isSecret,
              cloudType: image.cloudType,
            }
          }],
          permanent: formData.value.validityPeriod?.qualValidityPeriod === 0,
          startTime: typeof formData.value.validityPeriod?.startTime === 'number'
            ? formData.value.validityPeriod.startTime
            : (formData.value.validityPeriod?.startTime ? +formData.value.validityPeriod.startTime : 0),
          endTime: typeof formData.value.validityPeriod?.endTime === 'number'
            ? formData.value.validityPeriod.endTime
            : (formData.value.validityPeriod?.endTime ? +formData.value.validityPeriod.endTime : 0),
          indexId: index === 0 ? '889821130321592320' : '889821308189442048'
        }))

      const qualificationList = buildFileAttachmentList()

      const payload: IMerchantAuthorizationDraft = {
        // 证件类型
        certificateType: formData.value.idType,
        // 证件号码
        certificateNumber: formData.value.idNumber,
        // 法人姓名
        authorizationName: formData.value.name,
        // 是否通过人脸识别
        faceIdentification: !!formData.value?.faceIdentificationOrder,
        // 资质列表
        qualificationList,
        // 认证类型
        authorizationType: formData.value.verifyMethod === VerifyMethod.FACE ? 'PERSONAL_IDENTITY' : 'FOUR_ELEMENTS',
        // 人脸识别订单号
        faceIdentificationOrder: formData.value.faceIdentificationOrder || undefined,
        // 手机号
        phoneNumber: formData.value.phone || '',
        // 验证码
        verifyCode: formData.value.verifyCode || '',
      }

      // 提交法人信息
      await postAuthorizationSave({
        merchantAuthorizationDraft: payload,
      })

      // 同步数据到store，用于返回时回显
      // 构建法人资质信息，与认领模块格式保持一致
      const legalPersonQualData = {
        name: formData.value.name,
        idType: formData.value.idType,
        idNumber: formData.value.idNumber,
        idFrontSide: formData.value.idImages[0] ? {
          url: formData.value.idImages[0].url || '',
          width: formData.value.idImages[0].width,
          height: formData.value.idImages[0].height
        } : undefined,
        idBackSide: formData.value.idImages[1] ? {
          url: formData.value.idImages[1].url || '',
          width: formData.value.idImages[1].width,
          height: formData.value.idImages[1].height
        } : undefined,
        idValidityPeriod: formData.value.validityPeriod ? {
          qualValidityPeriod: formData.value.validityPeriod.qualValidityPeriod,
          startTime: formData.value.validityPeriod.startTime || 0,
          endTime: formData.value.validityPeriod.endTime || 0
        } : undefined,
        // 入驻特有字段
        certificateAddress: formData.value.certificateAddress || '',
        issuingAuthority: formData.value.issuingAuthority || '',
        verifyMethod: formData.value.verifyMethod,
        phone: formData.value.phone || '',
        faceIdentificationOrder: formData.value.faceIdentificationOrder || ''
      }

      // 同步到ClaimShopStore
      store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', {
        poiLegalPersonQual: legalPersonQualData
      })

      // 获取专业号数据并跳转
      handleVerify(verifyAlertRef)
    } catch (error: any) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: error?.message || '提交失败，请重试',
        duration: 2000,
      })
    } finally {
      isSubmitting.value = false // 结束提交loading
    }
  }

  // 处理返回上一步
  const handlePrevious = () => {
    router.go(-1)
  }

  const businessName = ref('')
  const getBusinessInfo = async () => {
    try {
      const response = await postSubjectQuery({})
      if (!response) {
        return ''
      }
      const { merchantCompanyDraft = {} } = response
      const { companyName, authorizationName } = merchantCompanyDraft
      businessName.value = companyName || ''
      formData.value.name = authorizationName || ''
    } catch (error) {
      return ''
    }
  }

  // 协议点击处理
  const handleAgreementClick = (index: number) => {
    // 查询商户企业名称
    const agreementList = [
      {
        title: '《小红书生活服务平台商户服务协议》',
        url: 'https://agree.xiaohongshu.com/h5/terms/ZXXY20230617001/-1',
      },
      {
        title: '《平安银行“平安结算通”商户服务协议》',
        url: `https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/jzbxy.html?name=${businessName.value}`,
      },
      {
        title: '《小红书本地生活品牌承诺函》',
        url: 'https://agree.xiaohongshu.com/h5/terms/ZXXY20240607001/-1',
      },
    ]
    const agreement = agreementList[index]
    if (agreement) {
      window.open(agreement.url, '_blank')
    }
  }

  // 清理定时器
  const cleanup = () => {
    if (codeTimer.value) {
      clearInterval(codeTimer.value)
      codeTimer.value = null
    }

    // 清理人脸识别轮询定时器
    stopFacePolling()

    // 清理页面可见性监听器
    if (visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', visibilityChangeHandler)
      visibilityChangeHandler = null
    }
  }

  // 预览/编辑 初始化数据
  const init = async () => {
    isInitializing.value = true // 开始初始化loading

    // 对于新建状态，优先检查store中是否有法人信息草稿数据
    if (!baseForm.isPreview.value && !isEdit.value) {
      try {
        const storeData = store.state.ClaimShopStore?.claimStorePageFormData
        const hasStoreDraftData = storeData?.poiLegalPersonQual && (
          storeData.poiLegalPersonQual.name
          || storeData.poiLegalPersonQual.idNumber
          || storeData.poiLegalPersonQual.idFrontSide?.url
          || storeData.poiLegalPersonQual.idBackSide?.url
        )

        if (hasStoreDraftData) {
          // 如果store中有法人草稿数据，使用草稿数据回显
          // console.log('检测到法人信息草稿数据，进行回显')
          const legalPersonQual = storeData.poiLegalPersonQual

          // 填充基础信息
          formData.value.name = legalPersonQual.name || ''
          formData.value.idType = legalPersonQual.idType || formData.value.idType
          formData.value.idNumber = legalPersonQual.idNumber || ''

          // 填充证件图片
          formData.value.idImages = [{}, {}]
          if (legalPersonQual.idFrontSide?.url) {
            formData.value.idImages[0] = {
              url: legalPersonQual.idFrontSide.url,
              desc: '身份证人像面',
              width: legalPersonQual.idFrontSide.width,
              height: legalPersonQual.idFrontSide.height
            }
          }
          if (legalPersonQual.idBackSide?.url) {
            formData.value.idImages[1] = {
              url: legalPersonQual.idBackSide.url,
              desc: '身份证国徽面',
              width: legalPersonQual.idBackSide.width,
              height: legalPersonQual.idBackSide.height
            }
          }

          // 填充有效期信息
          if (legalPersonQual.idValidityPeriod) {
            formData.value.validityPeriod = {
              qualValidityPeriod: legalPersonQual.idValidityPeriod.qualValidityPeriod,
              startTime: legalPersonQual.idValidityPeriod.startTime || 0,
              endTime: legalPersonQual.idValidityPeriod.endTime || 0
            }
          }

          // 填充入驻特有字段
          if (legalPersonQual.certificateAddress) {
            formData.value.certificateAddress = legalPersonQual.certificateAddress
          }
          if (legalPersonQual.issuingAuthority) {
            formData.value.issuingAuthority = legalPersonQual.issuingAuthority
          }
          if (legalPersonQual.verifyMethod) {
            formData.value.verifyMethod = legalPersonQual.verifyMethod
          }
          if (legalPersonQual.phone) {
            formData.value.phone = legalPersonQual.phone
          }
          if (legalPersonQual.faceIdentificationOrder) {
            formData.value.faceIdentificationOrder = legalPersonQual.faceIdentificationOrder
          }

          // 更新身份证解析状态
          baseForm.isIdParsed.value = true

          // console.log('法人信息草稿回显完成:', formData.value)
        }
      } catch (error) {
        console.error('获取法人信息草稿失败:', error)
      } finally {
        isInitializing.value = false // 结束初始化loading
      }
      return
    }

    // 预览模式或编辑模式才执行以下逻辑

    try {
      const { merchantAuthorizationDraft } = await postAuthorizationQuery({
        applyId: shopApplyState?.value?.id ?? '',
      })

      if (merchantAuthorizationDraft) {
        const {
          certificateType,
          certificateNumber,
          authorizationName,
          faceIdentification,
          qualificationList = [],
          faceIdentificationOrder,
          phoneNumber,
          verifyCode,
          certificateAddress,
        } = merchantAuthorizationDraft

        // 基础信息
        formData.value.name = authorizationName || ''
        formData.value.idType = certificateType || formData.value.idType // 默认身份证
        formData.value.idNumber = certificateNumber || ''

        // 处理OCR解析的额外字段
        if (certificateAddress) {
          formData.value.certificateAddress = certificateAddress
        }

        // 处理证件图片 - 现在是两个独立的qualification对象
        // 重置图片数组
        formData.value.idImages = [{}, {}]

        // 查找正面和反面资质 - 优先使用qualificationCode进行精确匹配
        const frontQualification = qualificationList.find(
          qual => qual.qualificationCode === '889821130321592320'
                  || qual.qualificationName?.includes('正面')
        )
        const backQualification = qualificationList.find(
          qual => qual.qualificationCode === '889821308189442048'
                  || qual.qualificationName?.includes('反面')
        )

        // 处理正面图片 (index 0)
        if (frontQualification?.fileAttachmentList?.[0]) {
          const frontFile = frontQualification.fileAttachmentList[0]
          formData.value.idImages[0] = {
            url: frontFile.uploaderInfoModel?.url || '',
            desc: '身份证人像面',
            fileName: frontFile.fileName,
            fileType: frontFile.fileType,
            height: frontFile.height,
            width: frontFile.width,
            fileId: frontFile.uploaderInfoModel?.fileId,
            bizName: frontFile.uploaderInfoModel?.bizName,
            scene: frontFile.uploaderInfoModel?.scene,
            isSecret: frontFile.uploaderInfoModel?.isSecret,
            cloudType: frontFile.uploaderInfoModel?.cloudType
          }
        }

        // 处理背面图片 (index 1)
        if (backQualification?.fileAttachmentList?.[0]) {
          const backFile = backQualification.fileAttachmentList[0]
          formData.value.idImages[1] = {
            url: backFile.uploaderInfoModel?.url || '',
            desc: '身份证国徽面',
            fileName: backFile.fileName,
            fileType: backFile.fileType,
            height: backFile.height,
            width: backFile.width,
            fileId: backFile.uploaderInfoModel?.fileId,
            bizName: backFile.uploaderInfoModel?.bizName,
            scene: backFile.uploaderInfoModel?.scene,
            isSecret: backFile.uploaderInfoModel?.isSecret,
            cloudType: backFile.uploaderInfoModel?.cloudType
          }
        }

        // 处理证件有效期 - 从任意一个qualification获取（它们应该是相同的）
        const validityQualification = frontQualification || backQualification
        if (validityQualification) {
          formData.value.validityPeriod = {
            qualValidityPeriod: validityQualification.permanent ? 0 : 1, // 0表示永久有效
            startTime: typeof validityQualification.startTime === 'number'
              ? validityQualification.startTime
              : (validityQualification.startTime ? +validityQualification.startTime : 0),
            endTime: typeof validityQualification.endTime === 'number'
              ? validityQualification.endTime
              : (validityQualification.endTime ? +validityQualification.endTime : 0),
          }
        }

        // 初始化验证方式相关字段
        // 如果已经通过人脸识别，设置为人脸验证方式
        if (faceIdentification) {
          formData.value.verifyMethod = VerifyMethod.FACE
          // 存储人脸识别订单号
          formData.value.faceIdentificationOrder = faceIdentificationOrder
          // 注意：faceKey 在编辑模式下通常不需要，因为它是临时的查询密钥
        } else {
          formData.value.verifyMethod = VerifyMethod.PHONE
          // 初始化手机号和验证码
          formData.value.phone = phoneNumber || ''
          formData.value.verifyCode = verifyCode || ''
        }

        // 不设置协议同意状态，让用户重新勾选确认
        formData.value.agreementAccepted = false // 让用户重新勾选

        // 更新身份证解析状态
        baseForm.isIdParsed.value = true

        // store 中存储原始数据
        console.log('🔥 存储原始数据:', formData.value)
        store.commit('rejectReason/SET_LEGAL_PERSON_ORIGINAL_DATA', cloneDeep(formData.value))
      }
    } catch (error) {
      // console.error('初始化法人信息失败:', error)
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: '获取法人信息失败',
        duration: 2000,
      })
    } finally {
      isInitializing.value = false // 结束初始化loading
    }
  }

  onMounted(() => {
    getBusinessInfo()
    init()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    legalPersonFormRef,
    // 继承基础功能
    ...baseForm,

    // 覆盖表单规则
    formRules,

    verifyMethodSheetVisible,
    verifyMethodText,
    verifyMethodPickerValue,

    verifyMethodPickerColumns,
    showSendButton,
    canSendCode,
    codePlaceholder,
    sendCodeButtonText,
    isSubmitDisabled,
    submitButtonText,

    // 添加loading状态
    isSubmitting,
    isInitializing,
    isSendingCode,

    // 入驻模块特有的方法
    confirmVerifyMethodPicker,
    sendVerifyCode,
    validateVerifyCode,
    showVerifyMethodSheet,
    hideVerifyMethodSheet,
    handleAgreementClick,
    handleSubmit,
    handlePrevious,
    handleInfoExtracted, // 重写的OCR信息处理方法
    handleFormValidation,
    handleValidationError,
  }
}
