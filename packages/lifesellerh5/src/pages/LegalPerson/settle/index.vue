<template>
  <div class="settle-legal-person-page">
    <header>
      <Header>
        <template #right>
          <div class="claim-shop-header" @click="handleCustomerServiceClick">
            <Icon icon-name="StoreChat" size="16"></Icon>
            <Text class="customer">客服</Text>
          </div>
        </template>
      </Header>
    </header>
    <Steps type="CLAIM_SHOP" />
    <main>
      <!-- 初始化loading遮罩 -->
      <div v-if="isInitializing">
        <LoadingNext />
      </div>

      <LanguageProvider>
        <LegalPersonForm
          ref="legalPersonFormRef"
          :form-data="formData"
          :form-rules="formRules"
          :is-settle="true"
          :is-preview="isPreview"
          :is-id-parsed="isIdParsed"
          :handle-info-extracted="handleInfoExtracted"
          @submitSuccess="nextStep"
          @submitError="handleValidationError"
        >
          <!-- 入驻模块特有的额外字段 -->
          <template #extra-fields="{ formData, isPreview }">
            <!-- 验证方式 -->
            <FormItem
              label="验证方式"
              name="verifyMethod"
              :value="formData.verifyMethod"
              :class="['form-item', formData.verifyMethod !== 'phone' ? 'form-item-last' : '']"
              :required-style="{color: '#FF2442'}"
            >
              <div class="selector" @click="showVerifyMethodSheet">
                <Text v-if="verifyMethodText" class="text-style">{{ verifyMethodText }}</Text>
                <Text v-else class="selector-placeholder">请选择</Text>
                <OnixIcon
                  v-if="!isPreview && formData.verifyMethod === 'phone'"
                  class="onix-icon-16"
                  icon="arrowRightRightM"
                />
                <!-- 如果选择人脸验证，并且通过 -->
                <Icon
                  v-if="formData.verifyMethod === 'face'"
                  :icon="SucceedC"
                  size="16"
                  style="margin-left: 4px;"
                />
              </div>
            </FormItem>

            <!-- 手机号 (验证方式为手机号验证时显示) -->
            <template v-if="formData.verifyMethod === 'phone'">
              <FormItem
                label="手机号"
                name="phone"
                :value="formData.phone"
                class="form-item"
                :required-style="{color: '#FF2442'}"
              >
                <TextField
                  v-model="formData.phone"
                  placeholder="请输入手机号"
                  input-align="right"
                  :clearable="!!formData.phone"
                  :disabled="isPreview"
                  class="text-field"
                  :input-style="{
                    color: 'rgba(0, 0, 0, 0.62)'
                  }"
                  style="padding: 0 !important;"
                />
              </FormItem>
            </template>

            <!-- 验证码 (验证方式为手机号验证时显示) -->
            <template v-if="formData.verifyMethod === 'phone'">
              <FormItem
                label="验证码"
                name="verifyCode"
                :value="formData.verifyCode"
                class="form-item form-item-last"
                :required-style="{color: '#FF2442'}"
              >
                <div class="verify-code-container">
                  <TextField
                    v-show="!showSendButton"
                    v-model="formData.verifyCode"
                    input-align="right"
                    :placeholder="codePlaceholder"
                    :clearable="!!formData.verifyCode"
                    :disabled="isPreview"
                    class="verify-code-input"
                    :input-style="{
                      color: 'rgba(0, 0, 0, 0.62)',
                      fontSize: formData.verifyCode ? '16px' : '14px'
                    }"
                    style="padding: 0 !important;font-size: var('--b2-font-size') !important;"
                  />
                  <Button
                    v-show="showSendButton && !isPreview"
                    type="primary"
                    variant="text"
                    size="small"
                    :disabled="!canSendCode || isSendingCode"
                    :style="{
                      fontSize: '14px',
                      color: '#FF2442 !important',
                      weight: '400',
                      lineHeight: '20px',
                    }"
                    @click="sendVerifyCode"
                  >
                    {{ sendCodeButtonText }}
                  </Button>
                </div>
              </FormItem>
            </template>
          </template>

          <!-- 入驻模块特有的协议确认 -->
          <template #footer="{ formData, isPreview }">
            <div class="agreement-section">
              <CheckBox
                v-model="formData.agreementAccepted"
                :value="1"
                label=""
                size="small"
                :disabled="isPreview"
                class="agreement-checkbox"
              />
              <div class="agreement-text">
                <p>我已阅读并同意<span @click="handleAgreementClick(0)">《小红书生活服务平台商户服务协议》</span>、<span @click="handleAgreementClick(1)">《平安银行"平安结算通"商户服务协议》</span>、<span @click="handleAgreementClick(2)">《小红书本地生活品牌承诺函》</span></p>
              </div>
            </div>
          </template>
        </LegalPersonForm>

        <!-- 验证方式选择弹窗 -->
        <Picker
          :columns="verifyMethodPickerColumns"
          :value="verifyMethodPickerValue"
          :visible="verifyMethodSheetVisible"
          :close-type="SheetsType.SheetsActionType.text"
          :cancel-type="SheetsType.SheetsActionType.text"
          :cancel-text="'取消'"
          :label="'请选择验证方式'"
          @confirm="confirmVerifyMethodPicker"
          @cancel="hideVerifyMethodSheet"
        />
      </LanguageProvider>
    </main>

    <!-- 入驻模块特有的上一步/下一步按钮 -->
    <footer>
      <Button
        size="large"
        :style="{width: '173px'}"
        @click="handlePrevious"
      >
        上一步
      </Button>
      <Button
        type="primary"
        size="large"
        :style="{width: '173px'}"
        :variant="isSubmitDisabled ? 'disabled' : 'fill'"
        :disabled="isSubmitDisabled || isSubmitting"
        @click="handleFormValidation"
      >
        {{ submitButtonText }}
      </Button>
    </footer>
    <VerifyAlert
      ref="verifyAlertRef"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import {
    Button,
    LanguageProvider,
    Picker,
    SheetsType,
    FormItem,
    TextField,
    Text,
    CheckBox,
    Icon
  } from '@xhs/reds-h5-next'
  import { SucceedC } from '@xhs/reder-icon-svg-ReDs_icon'
  import OnixIcon from '@xhs/onix-icon'
  // import { useRoute } from 'vue-router'

  // 组件
  import Header from '~/components/header/index.vue'
  import LegalPersonForm from '~/components/LegalPersonForm/index.vue'
  import VerifyAlert from '~/components/VerifyAlert/Index.vue'
  import Steps from '~/components/Steps/index.vue'
  import LoadingNext from '~/components/loading-next/index.vue'

  import { useSettleLegalPersonForm } from './composables/useSettleLegalPersonForm'
  import '~/assets/svg/arrowRightRightM.svg'

  import '~/assets/svg/iconRight2.svg'

  // 方法
  import { jumpContactXhsSupport } from '~/utils/shopPage'

  const verifyAlertRef = ref()

  const nextStep = async () => {
    await handleSubmit()
  }

  // 使用入驻模块专用的 composable
  const {
    legalPersonFormRef,
    // 数据
    formData,
    formRules,
    isPreview,
    isIdParsed,

    // 验证方式相关
    verifyMethodSheetVisible,
    verifyMethodText,
    verifyMethodPickerColumns,
    verifyMethodPickerValue,

    // 验证码相关
    showSendButton,
    canSendCode,
    codePlaceholder,
    sendCodeButtonText,

    // 计算属性
    isSubmitDisabled,
    submitButtonText,

    // loading状态
    isSubmitting,
    isInitializing,
    isSendingCode,

    // 方法
    showVerifyMethodSheet,
    hideVerifyMethodSheet,
    confirmVerifyMethodPicker,
    sendVerifyCode,
    handlePrevious,
    handleAgreementClick,
    handleInfoExtracted, // 获取重写的OCR信息处理方法
    handleFormValidation,
    handleValidationError,
    handleSubmit
  } = useSettleLegalPersonForm(verifyAlertRef)

  // 客服点击处理
  const handleCustomerServiceClick = () => {
    jumpContactXhsSupport()
  }
</script>

<style lang="stylus" scoped>
p
  margin 0 !important
// 页面布局
.settle-legal-person-page
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5
  padding-top 24px
  // iOS 安全区域适配 - 顶部
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

  header
    flex 0 0 auto

    .customer
      color rgba(0, 0, 0, 0.8)

    .claim-shop-header
      display flex
      align-items center
      justify-content center

  main
    flex 1 1 0
    min-height 0
    padding 0 16px
    margin-top 16px
    overflow auto
    -webkit-overflow-scrolling touch

  footer
    display flex
    align-items center
    justify-content space-between
    flex-shrink 0
    min-height 50px
    gap 15.5px
    padding 8px 16px
    // iOS 安全区域适配 - 底部
    @supports (padding-bottom: constant(safe-area-inset-bottom))
      padding-bottom constant(safe-area-inset-bottom)
    @supports (padding-bottom: env(safe-area-inset-bottom))
      padding-bottom env(safe-area-inset-bottom)

:deep(.form-item)
    padding 12px 16px 12px 0 !important
    margin-bottom 0
    border-bottom 0.5px solid rgba(0, 0, 0, 0.08)

    &:last-child
      border-bottom none

    // 第一个表单项的右上角圆角
    &.form-item-first
      border-top-right-radius 8px

    // 认领模式最后一个表单项的右下角圆角
    &.form-item-last
      border-bottom-right-radius 8px

:deep(.selector)
  display flex
  align-items center
  justify-content space-between
  cursor pointer
  .selector-placeholder
    color rgba(0, 0, 0, 0.45)
    font-family "PingFang SC"
    font-size 16px
    font-style normal
    font-weight 400

.agreement-checkbox
  flex-wrap wrap
  align-content flex-start

</style>
