import { computed } from 'vue'
import type { ComputedRef } from 'vue'
import { useRouter } from 'vue-router'
import { AuditStatus } from '../type'
import { createActionConfig, ROUTE_MAP, FooterAction } from '../config/actionConfig'

export const useAuditActions = (
  shopId: string,
  applyId: string,
  shopBizApplyType: string,
  currentStatus: ComputedRef<AuditStatus | null>
) => {
  const router = useRouter()

  // 操作处理函数
  const handleReapply = () => {
    const routerName = ROUTE_MAP[currentStatus.value as AuditStatus]

    if (routerName) {
      router.push({
        name: routerName,
        query: {
          applyId,
          shopId,
          shopBizApplyType,
          secondChannel: 'fill_qual'
        }
      })
    }
  }

  const handleViewDetail = () => {
    console.log('查看详情')
  }

  const handleCancel = () => {
    console.log('取消申请')
  }

  // 按钮配置
  const actionConfig = createActionConfig({
    handleReapply,
    handleViewDetail,
    handleCancel
  })

  // 当前状态的操作按钮
  const footActions = computed((): FooterAction[] => {
    if (!currentStatus.value) return []
    return actionConfig[currentStatus.value] || []
  })

  return {
    footActions,
    handleReapply,
    handleViewDetail,
    handleCancel
  }
}
