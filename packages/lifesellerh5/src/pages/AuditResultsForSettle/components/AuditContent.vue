<template>
  <div class="audit-content">
    <!-- 拒绝状态特殊处理，支持展开收起 -->
    <template v-if="props.status === AuditStatus.REJECTED">
      <div class="audit-results-page-content-item">
        <div class="audit-results-page-content-item-desc">
          您的申请未通过，{{ props.applyId || props.info ? `申请 ID：${props.applyId || props.info}` : '' }}
        </div>
        <div class="audit-results-page-content-item reject-background">
          <div class="audit-results-page-content-item-title-reject">审核意见</div>

          <!-- 有驳回详情列表时 -->
          <template v-if="validRejectDetails.length > 0">
            <!-- 显示的驳回详情（根据展开状态决定显示数量） -->
            <div
              v-for="(detail, index) in displayedRejectDetails"
              :key="index"
              class="audit-results-page-content-item-desc reject-detail-item"
            >
              <strong>{{ detail.fieldName }}</strong> : {{ detail.rejectContent }}
            </div>

            <!-- 展开/收起按钮 -->
            <div
              v-if="validRejectDetails.length > 3"
              class="expand-toggle"
              @click="toggleExpand"
            >
              {{ isExpanded ? '收起' : '展开' }}
            </div>
          </template>

          <!-- 没有驳回详情时显示默认原因 -->
          <div v-else class="audit-results-page-content-item-desc">
            {{ props.reason || '请根据意见修改后重新提交。' }}
          </div>
        </div>
      </div>
    </template>

    <!-- 其他状态使用原有的 v-html 渲染 -->
    <template v-else>
      <!-- eslint-disable-next-line vue/no-v-html -->
      <div v-html="contentHtml" />
    </template>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import { generateContentHtml } from '../config/contentConfig'
  import { AuditStatus } from '../type'
  import { IRejectDetailList } from '~/services/edith_get_deposit_detail'

  interface Props {
    status: AuditStatus | null
    applyId?: string
    reason?: string
    auditPageInfo?: string
    rejectDetailList?: IRejectDetailList[]
    // 兼容旧的info字段
    info?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    status: null,
    applyId: '',
    reason: '',
    auditPageInfo: '',
    rejectDetailList: () => [],
    info: ''
  })

  // 展开状态
  const isExpanded = ref(false)

  // 有效的驳回详情（过滤掉空内容）
  const validRejectDetails = computed(() => props.rejectDetailList?.filter(detail => detail.rejectContent && detail.rejectContent.trim()) || [])

  // 显示的驳回详情（根据展开状态决定数量）
  const displayedRejectDetails = computed(() => {
    const details = validRejectDetails.value
    if (details.length <= 3 || isExpanded.value) {
      return details
    }
    return details.slice(0, 2)
  })

  // 切换展开状态
  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
  }

  // 非拒绝状态的内容HTML
  const contentHtml = computed(() => {
    if (props.status === AuditStatus.REJECTED) {
      return ''
    }
    return generateContentHtml(props.status, {
      applyId: props.applyId || props.info,
      reason: props.reason,
      auditPageInfo: props.auditPageInfo,
      rejectDetailList: props.rejectDetailList
    })
  })
</script>

<style lang="stylus" scoped>
.audit-content
  padding-bottom 80px
// 使用:deep()选择器让样式能够应用到动态生成的HTML
:deep(.audit-results-page-content-item-title)
  color: var(--Light-colorGrayLevel1, #333);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 17px; /* 113.333% */
  margin-bottom: 8px
  text-align: center

:deep(.audit-results-page-content-item-title-reject)
  color: var(--Light-colorGrayLevel1, #333);
  font-size: 15px;
  font-style: normal;
  font-weight: 500;
  line-height: 17px; /* 113.333% */
  margin-bottom: 8px
  text-align: left

:deep(.audit-results-page-content-item-desc)
  color: var(--Light-Labels-Description, rgba(0, 0, 0, 0.45))
  font-size: 12px
  font-style: normal
  font-weight: 400
  line-height: 18px
  margin-bottom: 12px
  text-align: center

:deep(.reject-background)
  background: #FFF
  padding: 16px
  border-radius: 8px

.reject-detail-item
  margin-bottom: 8px
  text-align: left

.expand-toggle
  display: flex
  align-items: center
  justify-content: center
  padding: 8px
  margin-top: 8px
  color: var(--Light-Labels-Primary, RGB(61, 138, 245))
  font-size: 12px
  font-weight: 400
  cursor: pointer
  user-select: none

  &:hover
    opacity: 0.8

.expand-icon
  margin-left: 4px
  font-size: 10px
  color: var(--Light-Labels-Primary, RGB(61, 138, 245))
</style>
