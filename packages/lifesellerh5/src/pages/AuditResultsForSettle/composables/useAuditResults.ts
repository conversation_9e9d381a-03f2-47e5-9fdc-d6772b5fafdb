import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'
import { getApplyQuery, IData } from '~/services/edith_get_apply_query'
import { getDepositDetail, IData as IDepositDetailData } from '~/services/edith_get_deposit_detail'
import {
  AuditStatus,
  AuditStatusText
} from '../type'

// 结果类型映射 - 根据 getApplyQuery 接口的 applyStatus 字段
const resultTypeMap = {
  [AuditStatus.AUDITING]: 'Info',
  [AuditStatus.APPROVED]: 'Success',
  [AuditStatus.REJECTED]: 'Warning',
  [AuditStatus.NO_DATA]: 'Info'
}

// 申请状态枚举映射 - 根据 getApplyQuery 接口的 applyStatus 字段
const APPLY_STATUS_MAP: Record<string, AuditStatus> = {
  // 审核中相关状态
  TO_SUBMIT: AuditStatus.AUDITING, // 待提交 → 展示审核中
  PRE_HANDLING: AuditStatus.AUDITING, // 预处理 → 展示审核中
  TO_SP_AUDIT: AuditStatus.AUDITING, // 审核中 → 展示审核中
  AUDIT_PASS: AuditStatus.AUDITING, // 审核通过还没完成开店 → 展示审核中

  // 审核通过相关状态
  FINISHED: AuditStatus.APPROVED, // 完成 → 展示审核通过

  // 审核拒绝相关状态
  AUDIT_REJECT: AuditStatus.REJECTED // 审核驳回 → 展示审核拒绝
}

export const useAuditResults = () => {
  const route = useRoute()
  const store = useStore()

  // 从路由获取用户ID，getApplyQuery 接口需要 userId 参数
  const { userId } = route.query as {
    userId?: string
  }

  const result = ref<IData | null>(null)
  const depositDetail = ref<IDepositDetailData | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 转换申请状态为审核状态
  const convertApplyStatus = (applyStatus?: string): AuditStatus => {
    if (!applyStatus) return AuditStatus.NO_DATA

    const statusStr = String(applyStatus).toUpperCase()
    return APPLY_STATUS_MAP[statusStr] || AuditStatus.NO_DATA
  }

  // 计算属性
  const resultType = computed(() => {
    const auditStatus = convertApplyStatus(result.value?.applyStatus)
    return resultTypeMap[auditStatus]
  })

  const resultTitle = computed(() => {
    const auditStatus = convertApplyStatus(result.value?.applyStatus)
    return AuditStatusText[auditStatus]
  })

  // 拒绝原因
  const reason = computed(() => result.value?.rejectReason || '')

  // 申请ID信息
  const resultInfo = computed(() => (result.value?.id ? String(result.value.id) : ''))

  // 当前审核状态
  const currentStatus = computed(() => convertApplyStatus(result.value?.applyStatus))

  // 店铺ID
  const shopId = computed(() => result.value?.sellerId || '')

  // 申请ID
  const applyId = computed(() => (result.value?.id ? String(result.value.id) : ''))

  // 申请类型（用于操作按钮配置）
  const shopBizApplyType = computed(() => result.value?.applyType || '')

  // 是否为空状态（查询失败或无数据）
  const isEmpty = computed(() => error.value !== null || (!loading.value && !result.value))

  // 是否显示正常内容
  const showContent = computed(() => !loading.value && !isEmpty.value && result.value)

  // 审核页面展示内容（富文本）
  const auditPageContent = computed(() => result.value?.auditPageInfo || '')

  // 驳回详情数据 - 仅使用接口数据
  const rejectDetailList = computed(() => {
    // 只有在有接口数据时才更新store并返回数据
    if (depositDetail.value?.rejectDetailList) {
      store.commit('rejectReason/SET_DEPOSIT_DETAIL', depositDetail.value)
      return depositDetail.value.rejectDetailList
    }
    return []
  })

  // 获取审核结果
  const getAuditResults = async () => {
    loading.value = true
    error.value = null

    try {
      const res = await getApplyQuery({
        userId
      })

      if (!res) {
        error.value = '未查询到相关数据'
        return
      }

      result.value = res

      // 如果状态为驳回，获取驳回详情
      const auditStatus = convertApplyStatus(res.applyStatus)
      if (auditStatus === AuditStatus.REJECTED) {
        await getDepositDetails()
      }
    } catch (err) {
      console.error('获取申请状态失败:', err)
      error.value = '网络连接失败，请检查网络后重试'
    } finally {
      loading.value = false
    }
  }

  // 获取驳回详情
  const getDepositDetails = async () => {
    try {
      store.commit('rejectReason/SET_LOADING', true)
      store.commit('rejectReason/SET_ERROR', null)

      const detailRes = await getDepositDetail({})
      depositDetail.value = detailRes

      // 将驳回详情存储到store中
      store.commit('rejectReason/SET_DEPOSIT_DETAIL', detailRes)
    } catch (err) {
      console.error('获取驳回详情失败:', err)
      const errorMsg = err instanceof Error ? err.message : '获取驳回详情失败'
      store.commit('rejectReason/SET_ERROR', errorMsg)
      // 获取驳回详情失败不影响主流程，只记录错误
    } finally {
      store.commit('rejectReason/SET_LOADING', false)
    }
  }

  // 重新查询
  const retry = () => {
    getAuditResults()
  }

  return {
    // 状态
    result,
    depositDetail,
    loading,
    error,
    // 计算属性
    resultType,
    resultTitle,
    reason,
    resultInfo,
    currentStatus,
    isEmpty,
    showContent,
    auditPageContent, // 新增：审核页面富文本内容
    rejectDetailList, // 新增：驳回详情列表
    // 查询参数和结果
    shopId,
    applyId,
    shopBizApplyType,
    userId,
    // 方法
    getAuditResults,
    retry
  }
}
