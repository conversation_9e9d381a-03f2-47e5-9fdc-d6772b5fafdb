<template>
  <Loading v-if="loading"></Loading>
  <div class="claim-store-div">
    <header>
      <Header>
        <template #right>
          <div class="claim-shop-header" @click="() => jumpContactXhsSupport()">
            <OnixIcon class="onix-icon-16" icon="iconRight2"></OnixIcon>
            <Text class="customer">客服</Text>
          </div>
        </template>
      </Header>
    </header>
    <main>
      <!-- 步骤指示器 -->
      <Steps :type="STEP_TYPE.CLAIM_SHOP" />

      <div class="form-item-layout">
        <!-- 主营品类选择 -->
        <article class="form-item form-item-layout">
          <div class="form-item-header">
            <p class="title">主营品类</p>
            <div class="header-right">
              <!-- <div class="category-tip" @click="handleSkip">
                先跳过，直接提交审核
              </div> -->
            </div>
          </div>
          <div class="form-field">
            <label class="form-label">主营品类</label>
            <CategoryPicker
              v-model="selectedCategory"
              title="选择主营品类"
              :readonly="isView"
              @confirm="handleCategoryConfirm"
            />
          </div>
        </article>

        <!-- 使用抽离的组件 -->
        <QualificationSelector
          ref="qualificationSelectorRef"
          :model-value="qualificationData"
          :category-id="selectedCategoryId"
          :readonly="isView"
          :qualification-list="qualificationList"
          @qualification-change="handleQualificationChange"
        />
      </div>
    </main>

    <!-- 底部按钮 -->
    <footer>
      <div class="footer-buttons">
        <Button
          type="secondary"
          variant="outline"
          block
          @click="handlePrevious"
        >
          上一步
        </Button>
        <Button
          type="primary"
          :disabled="!canSubmit"
          block
          @click="handleSubmit"
        >
          继续提交材料
        </Button>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
  import {
    ref, computed, onMounted, watch
  } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter, useRoute } from 'vue-router'
  import { Button, showToast, ToastType } from '@xhs/reds-h5-next'

  // 组件
  import OnixIcon from '@xhs/onix-icon'
  import Header from '~/components/header/index.vue'
  import QualificationSelector from '~/components/QualificationItem/CategoryQualificationSelector.vue'
  import CategoryPicker from '~/components/CategoryPicker/index.vue'
  import Steps from '~/components/Steps/index.vue'
  import { STEP_TYPE } from '~/components/Steps/stepConfig'
  import ROUTE_NAME from '~/constants/routename'
  import Loading from '~/components/loading-next/index.vue'

  // 服务
  import { getQueryQualificationConfigget } from '~/services/edith_get_query_qualification_configget'
  import { getQueryEffectiveQualification } from '~/services/edith_get_query_effective_qualification'

  // 方法
  import { jumpContactXhsSupport } from '~/utils/shopPage'

  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'
  import '~/assets/svg/iconRight2.svg'

  // 初始化
  const store = useStore()
  const router = useRouter()
  const route = useRoute()

  const isView = ref(false)

  // 组件引用
  const qualificationSelectorRef = ref()

  // 分类选择状态 - 现在直接是字符串类型（最后一层的分类ID）
  const selectedCategory = ref<string>('')

  // 页面状态
  const isSubmitting = ref(false)
  const submitError = ref<string | null>(null)

  const loading = ref(false)

  // 资质数据状态
  const qualificationList = ref<any[]>([])
  const isLoadingQualifications = ref(false)

  // 资质数据 - 用于传递给组件的初始数据
  const qualificationData = ref<Record<string, any>>({})

  // 计算属性 - 直接使用selectedCategory作为分类ID
  const selectedCategoryId = computed(() => selectedCategory.value || '')

  const canSubmit = computed(() => {
    if (!qualificationSelectorRef.value) return false

    // 如果没有选择分类，允许跳过提交
    if (!selectedCategoryId.value) return true

    const validation = qualificationSelectorRef.value.validateForm()
    return validation.valid
  })

  // 计算属性 - 响应式获取 store 数据
  const shopApplyState = computed(() => store.state.shopApplyStore?.shopApplyState)
  const storeData = computed(() => store.state.ClaimShopStore?.claimStorePageFormData)
  const applyId = computed(() => shopApplyState.value?.id)
  const shopId = computed(() => storeData.value?.shopId)

  // 数据初始化函数
  const initializeData = async () => {
    const currentStoreData = storeData.value
    const currentShopId = shopId.value
    const currentApplyId = applyId.value

    // 优先使用store中的数据进行回显
    if (currentStoreData?.categoryId || Object.keys(currentStoreData?.qualificationMap || {}).length > 0) {
      selectedCategory.value = currentStoreData.categoryId || ''
      qualificationData.value = currentStoreData.qualificationMap || {}

      // 如果有分类ID，加载对应的资质配置
      if (currentStoreData.categoryId) {
        await loadQualificationTypes(currentStoreData.categoryId)
      }
      return
    }

    // 如果store中没有数据，尝试从服务端获取
    if (currentShopId || currentApplyId) {
      try {
        loading.value = true
        const { effectiveQualification } = await getQueryEffectiveQualification({
          shopId: currentShopId,
          applyId: currentApplyId
        } as any)

        if (effectiveQualification?.qualificationMap) {
          // 从资质数据中获取分类ID，确保类型正确
          const qualificationData = effectiveQualification.qualificationMap
          const categoryId = String(qualificationData.categoryId || '')

          selectedCategory.value = categoryId
          qualificationData.value = qualificationData

          // 如果有分类ID，加载对应的资质配置
          if (categoryId) {
            await loadQualificationTypes(categoryId)
          }
        }
      } catch (error) {
        console.error('获取资质数据失败:', error)
        showToast({
          message: '获取资质失败',
          type: ToastType.ToastBuiltInType.TEXT,
          duration: 2000
        })
      } finally {
        loading.value = false
      }
    }
  }

  // 监听路由变化
  watch(
    () => route.name,
    newRoute => {
      if (newRoute === ROUTE_NAME.SUPPLEMENTARY_QUALIFICATIONS_STORE_CLAIM) {
        // 当路由返回到当前页面时，重新初始化数据
        initializeData()
      }
    }
  )

  // 页面挂载时初始化数据
  onMounted(() => {
    initializeData()
  })

  // 加载资质类型配置
  const loadQualificationTypes = async (categoryId: string) => {
    if (!categoryId) {
      qualificationList.value = []
      return
    }

    isLoadingQualifications.value = true

    try {
      const response = await getQueryQualificationConfigget({ categoryId })

      // 使用真实API数据，如果没有数据则使用空数组
      if (response?.qualificationGroupList && response.qualificationGroupList.length > 0) {
        qualificationList.value = response.qualificationGroupList
      } else {
        qualificationList.value = []
      }
    } catch (err) {
      qualificationList.value = []
      console.error('加载资质配置失败:', err)
    } finally {
      isLoadingQualifications.value = false
    }
  }

  // 处理分类选择确认 - 现在直接接收字符串类型的分类ID
  const handleCategoryConfirm = async (categoryId: string) => {
    selectedCategory.value = categoryId

    if (categoryId) {
      // 分类变化时清空之前的资质数据
      qualificationData.value = {}
      // 加载对应的资质配置
      await loadQualificationTypes(categoryId)
    }
  }

  // 处理资质数据变化
  const handleQualificationChange = (qualificationDataUpdate: Record<string, any>) => {
    // 比较数据是否真的发生了变化，避免不必要的更新
    const currentValue = JSON.stringify(qualificationData.value)
    const newValue = JSON.stringify(qualificationDataUpdate)

    if (currentValue !== newValue) {
      qualificationData.value = qualificationDataUpdate
      console.log('资质数据变化:', qualificationDataUpdate)
    }
  }

  // 处理上一步
  const handlePrevious = () => {
    router.back()
  }

  // 处理提交
  const handleSubmit = async () => {
    if (isSubmitting.value) return

    // 清除之前的错误
    submitError.value = null

    // 如果选择了分类，验证表单
    if (selectedCategoryId.value) {
      const validation = qualificationSelectorRef.value?.validateForm()
      if (!validation || !validation.valid) {
        submitError.value = validation?.errors.join(', ') || '表单验证失败'
        console.error('表单验证失败:', validation?.errors)
        return
      }
    }

    isSubmitting.value = true

    try {
      // 获取组件数据
      const formData = qualificationSelectorRef.value?.getFormData() || { qualificationMap: {} }

      // 过滤出有 mediaInfoList 且长度大于 0 的项
      const filteredQualificationMap = Object.fromEntries(
        Object.entries(formData.qualificationMap).filter(([, qualification]) => (qualification as any)?.mediaInfoList?.length > 0)
      )

      // 将数据存储到 ClaimShopStore
      store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', {
        categoryId: selectedCategoryId.value,
        qualificationMap: filteredQualificationMap
      })

      router.push({
        name: ROUTE_NAME.LEGAL_PERSON_STORE_CLAIM
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '提交失败，请重试'
      submitError.value = errorMessage
      console.error('提交失败:', error)
    } finally {
      isSubmitting.value = false
    }
  }

  // // 处理跳过
  // const handleSkip = () => {
  //   handleSubmit()
  // }
</script>

<style lang="stylus" scoped>
// 公共
p
  margin-block-start 0
  margin-block-end 0

.onix-icon-16
  min-width 16px
  min-height 16px

// 常见text样式
.common-text
  font-family "PingFang SC"
  font-size 16px
  font-style normal
  font-weight 400
  line-height 24px

// 底部备注样式
.footer-tip-view
  width 100%
  padding 8px 16px 12px 16px

// 页面
.claim-store-div
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5

  // iOS 安全区域适配 - 顶部
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

header
  flex-shrink 0
  width 100%
  z-index 100

  .customer
    color rgba(0, 0, 0, 0.8)

  .claim-shop-header
    display flex
    align-items center
    justify-content center

main
  flex 1
  width 100%
  padding 0 16px
  overflow-y auto
  overflow-x hidden
  background #f5f5f5
  padding 24px 16px 16px 16px

  &::-webkit-scrollbar
    width 4px
  &::-webkit-scrollbar-thumb
    background rgba(0, 0, 0, 0.2)
    border-radius 2px

.form-item {
  width: 100%;
  margin-bottom: 16px;

  .form-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 0 8px;
  }

  .title {
    margin: 0;
    color: rgba(0, 0, 0, 0.62);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    flex: 1;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.form-item-layout {
  margin-bottom: 16px;
}

.form-field {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 16px;
  background: #FFF;
  padding: 16px;
}

.form-label {
  color: var(--title);
  font-size: var(--b1-font-size);
  font-style: normal;
  font-weight: var(--b1-font-weight);
  line-height: var(--b1-line-height);
}

footer {
    background: #fff;
    flex: 0 0 auto;
    padding: 16px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;

    @supports (padding-bottom: env(safe-area-inset-bottom, 16px)) {
      padding-bottom: env(safe-area-inset-bottom, 16px);
    }
    @supports (padding-bottom: constant(safe-area-inset-bottom, 16px)) {
      padding-bottom: constant(safe-area-inset-bottom, 16px);
    }

  .footer-buttons {
    display: flex;
    gap: 12px;
  }
}

// 插槽内容样式
.category-tip {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;
  cursor: pointer;
}
</style>
