<template>
  <div class="claim-store-div">
    <header>
      <Header>
        <template #right>
          <div class="claim-shop-header" @click="jumpContactXhsSupport">
            <OnixIcon class="onix-icon-16" icon="iconRight2"></OnixIcon>
            <Text class="customer">客服</Text>
          </div>
        </template>
      </Header>
    </header>
    <main>
      <!-- 步骤指示器 -->
      <Steps :type="STEP_TYPE.CLAIM_SHOP" />

      <div class="form-item-layout">
        <!-- 使用抽离的组件 -->
        <QualificationSelectorForSettle
          ref="qualificationSelectorRef"
          :model-value="qualificationData"
          :category-id="categoryId"
          :qualification-groups="qualificationList"
          :readonly="isView"
          @qualification-change="handleQualificationChange"
        />
      </div>
    </main>

    <!-- 底部按钮 -->
    <footer>
      <div class="footer-buttons">
        <Button
          type="secondary"
          variant="outline"
          block
          @click="handlePrevious"
        >
          上一步
        </Button>
        <Button
          :disabled="!canSubmit"
          type="primary"
          block
          @click="handleSubmit"
        >
          继续提交材料
        </Button>
      </div>
    </footer>

    <!-- 全屏加载遮罩 -->
    <div v-if="isSubmitting" class="loading-overlay">
      <Loading />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter } from 'vue-router'
  import {
    Button,
    showToast,
    ToastType,
  } from '@xhs/reds-h5-next'

  import OnixIcon from '@xhs/onix-icon'
  import { cloneDeep } from 'lodash'

  // 组件
  import Header from '~/components/header/index.vue'
  import QualificationSelectorForSettle from '~/components/QualificationItem/categoryQualificationSeectorforSettle.vue'
  import Steps from '~/components/Steps/index.vue'
  import { STEP_TYPE } from '~/components/Steps/stepConfig'
  import { getQueryMerchantCategoryQualificationDraft } from '~/services/edith_get_query_merchant_category_qualification_draft'
  import { postSaveCategoryQualificationDraft } from '~/services/edith_post_save_category_qualification_draft'
  import { postBatchGetCategoryQualificationConfigFromPoi } from '~/services/edith_post_batch_get_category_qualification_config_from_poi'
  import { forBackend, forFrontend } from '~/utils/qualificationMapTransform'
  import { principal2SellerType } from './types'
  import Loading from '~/components/loading-next/index.vue'

  // 方法
  import { jumpContactXhsSupport } from '~/utils/shopPage'
  // 静态资源
  import '~/assets/svg/arrowRightRightM.svg'
  import '~/assets/svg/iconRight2.svg'

  // 初始化
  const store = useStore()
  const router = useRouter()

  // 从 ClaimShopStore 获取 categoryId
  const categoryId = computed(() => store.state.ClaimShopStore.claimStorePageFormData.xhsCategoryId)
  const applyId = computed(() => store.state.ClaimShopStore.claimStorePageFormData.id || store.state.ShopApplyStore.shopApplyState.id)
  const principalType = computed(() => store.state.ClaimShopStore.claimStorePageFormData.principalType)

  const sellerType = computed(() => principal2SellerType(principalType.value))

  const isView = ref(false)

  // const userInfo = computed(() => store.state.ShopUserInfoStore.userInfo)

  // 组件引用
  const qualificationSelectorRef = ref()
  const qualificationList = ref<any[]>([]) // 资质组列表，包含 qualificationGroupFunc 和 qualificationConfigList
  // 页面状态
  const isSubmitting = ref(false)
  const submitError = ref<string | null>(null)
  // 资质数据 - 用于传递给组件的初始数据
  const qualificationData = ref<Record<string, any>>({})

  const canSubmit = computed(() => {
    if (!qualificationSelectorRef.value) return false

    const validation = qualificationSelectorRef.value.validateForm()
    return validation.valid
  })

  // 查询类目资质草稿
  const getQualificationInfo = async () => {
    if (!applyId.value) {
      qualificationData.value = store.state.ClaimShopStore.claimStorePageFormData.qualificationMap
    } else {
      try {
        isSubmitting.value = true
        const response = await getQueryMerchantCategoryQualificationDraft({ applyId: applyId.value })
        if (response?.categoryQualificationList && response.categoryQualificationList.length > 0) {
          // 使用转换工具从草稿数据中提取资质数据
          qualificationData.value = forFrontend(response.categoryQualificationList)
        }
        // store 中存储原始数据
        store.commit('rejectReason/SET_QUALIFICATION_ORIGINAL_DATA', cloneDeep(qualificationData.value))
      } catch (error: any) {
        showToast({
          type: ToastType.ToastBuiltInType.ERROR,
          message: error.message || '获取资质草稿失败',
          duration: 2000
        })
        console.error('获取资质草稿失败:', error)
      } finally {
        isSubmitting.value = false
      }
    }
  }

  // 获取资质组列表（包含逻辑关系和资质配置）
  const getQualificationList = async () => {
    try {
      const response = await postBatchGetCategoryQualificationConfigFromPoi({
        poiCategoryId: categoryId.value,
        sellerType: sellerType.value
      })
      qualificationList.value = response.categoryqualificationList || []
    } catch (error: any) {
      showToast({
        type: ToastType.ToastBuiltInType.ERROR,
        message: error.message || '获取资质组列表失败',
        duration: 2000
      })
      console.error('获取资质组列表失败:', error)
    }
  }

  // 页面挂载时从 store 获取数据回显
  onMounted(async () => {
    await getQualificationInfo()
    await getQualificationList()
  })

  // 处理资质数据变化
  const handleQualificationChange = (qualificationDataUpdate: Record<string, any>) => {
    // 比较数据是否真的发生了变化，避免不必要的更新
    const currentValue = JSON.stringify(qualificationData.value)
    const newValue = JSON.stringify(qualificationDataUpdate)

    if (currentValue !== newValue) {
      qualificationData.value = qualificationDataUpdate
    }
  }

  // 处理上一步
  const handlePrevious = () => {
    router.back()
  }

  // 处理提交
  const handleSubmit = async () => {
    if (isSubmitting.value) return

    // 清除之前的错误
    submitError.value = null

    // 验证表单
    const validation = qualificationSelectorRef.value?.validateForm()
    if (!validation || !validation.valid) {
      submitError.value = validation?.errors.join(', ') || '表单验证失败'
      console.error('表单验证失败:', validation?.errors)
      return
    }
    // 获取组件数据
    const formData = qualificationSelectorRef.value?.getFormData()

    // 将数据存储到 ClaimShopStore
    store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', {
      categoryId: categoryId.value,
      qualificationMap: formData.qualificationMap
    })
    // 使用转换工具准备提交数据
    const categoryQualificationList = forBackend(formData.qualificationMap).filter(item => (item?.fileAttachmentList ?? []).length > 0)

    const submitData = {
      categoryQualificationList
    }

    try {
      isSubmitting.value = true
      await postSaveCategoryQualificationDraft(submitData)
      router.push({
        name: 'legalPersonSettle'
      })
    } catch (error: any) {
      console.error('提交失败:', error)
      showToast({
        type: ToastType.ToastBuiltInType.ERROR,
        message: error.message || '提交失败，请重试',
        duration: 2000
      })
      submitError.value = '提交失败，请重试'
    } finally {
      isSubmitting.value = false
    }
  }
</script>

<style lang="stylus" scoped>
// 公共
p
  margin-block-start 0
  margin-block-end 0

.onix-icon-16
  min-width 16px
  min-height 16px

// 常见text样式
.common-text
  font-family "PingFang SC"
  font-size 16px
  font-style normal
  font-weight 400
  line-height 24px

// 底部备注样式
.footer-tip-view
  width 100%
  padding 8px 16px 12px 16px

// 页面
.claim-store-div
  display flex
  flex-direction column
  width 100%
  height 100vh
  background #F5F5F5

  // iOS 安全区域适配 - 顶部
  @supports (padding-top: constant(safe-area-inset-top))
    padding-top constant(safe-area-inset-top)
  @supports (padding-top: env(safe-area-inset-top))
    padding-top env(safe-area-inset-top)

header
  flex-shrink 0
  width 100%
  z-index 100

  .customer
    color rgba(0, 0, 0, 0.8)

  .claim-shop-header
    display flex
    align-items center
    justify-content center

main
  flex 1
  width 100%
  padding 0 16px
  overflow-y auto
  overflow-x hidden
  background #f5f5f5
  padding 24px 16px 16px 16px

  &::-webkit-scrollbar
    width 4px
  &::-webkit-scrollbar-thumb
    background rgba(0, 0, 0, 0.2)
    border-radius 2px

.form-item {
  width: 100%;

  .title {
    margin: 0 0 12px 8px;
    color: rgba(0, 0, 0, 0.62);
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .form-item-box {
    width: 100%;
    height: 100%;
    padding-left: 16px;
    background: #FFF;

    &.border-radius-top {
      border-radius: 8px 8px 0 0;
    }
  }

  .item {
    padding: 16px 16px 16px 0;
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.08);

    &.border-top {
      border-top: none;
    }

    &.border-radius-top {
      border-radius: 8px 8px 0 0;
    }

    .sheets {
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      .sheets-empty {
        color: rgba(0, 0, 0, 0.45);
      }

      .onix-icon-16 {
        width: 16px;
        height: 16px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}

.form-item-layout {
  margin-bottom: 16px;
}

.form-field-qualification {
  margin-bottom: 80px;
}

.form-field {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 16px;
  background: #FFF;
  padding: 16px;
}

.form-label {
  color: var(--title);
  font-size: var(--b1-font-size);
  font-style: normal;
  font-weight: var(--b1-font-weight);
  line-height: var(--b1-line-height); /* 150% */
}

footer {
    background: #fff;
    flex: 0 0 auto;
    padding: 16px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;

    @supports (padding-bottom: env(safe-area-inset-bottom, 16px)) {
      padding-bottom: env(safe-area-inset-bottom, 16px);
    }
    @supports (padding-bottom: constant(safe-area-inset-bottom, 16px)) {
      padding-bottom: constant(safe-area-inset-bottom, 16px);
    }

  .footer-buttons {
    display: flex;
    gap: 12px;
  }
}

// 插槽内容样式
.category-tip {
  color: var(--link-accent, rgba(61, 138, 245, 1));
  font-size: var(--c2-font-size, 12px);
  font-style: normal;
  font-weight: var(--c2-font-weight, 400);
  line-height: var(--c2-line-height, 18px); /* 150% */
}

// 全屏加载遮罩样式
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background rgba(255, 255, 255, 0.5)
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .loading-spinner {
      width: 24px;
      height: 24px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3d8af5;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      color: rgba(0, 0, 0, 0.85);
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
