// vue
import {
  ref, reactive, computed, onMounted
} from 'vue'
import { useRouter } from 'vue-router'

// 依赖
import {
  showToast, ToastType,
} from '@xhs/reds-h5-next'

// 数据
import { useStore } from 'vuex'
import ROUTE_NAME from '~/constants/routename'
import { formItems, FormItemTypeEnum, defaultValueMap } from './content'
import { ClaimTypeEnum } from '~/enum/shop'

// 接口
import { getCanClaimPoiMerchant } from '~/services/shop'

export function useShopInfoPage() {
  // ==============初始化==============
  // 初始化路由
  const router = useRouter()

  // 初始化vuex
  const store = useStore()

  // 整个表单数据
  const {
    claimStorePageFormData,
  } = store.state.ClaimShopStore

  // 旧版信息
  const prevRouteParams = computed(() => store.state.ClaimShopStore.prevRouteParams)

  // 用户信息
  const {
    userInfo,
  } = store.state.ShopUserInfoStore

  // 初始化form表单响应式对象
  const formModel = reactive({})

  // 表单赋值，存在vuex数据先用vuex数据，不然用默认值
  function getInitFormData(poiShopInfo) {
    for (const item of formItems) {
      switch (item.type) {
        case FormItemTypeEnum.PoiShopInfo:

          formModel[item.valueKey] = poiShopInfo.poiId ? { ...claimStorePageFormData } : { ...defaultValueMap.poiShopInfo }
          break
        case FormItemTypeEnum.String:
          formModel[item.valueKey] = poiShopInfo?.[item.valueKey] ?? defaultValueMap.string
          break
        default:
          formModel[item.valueKey] = defaultValueMap[item.type] ?? ''
          break
      }
    }
  }

  // ==============alert弹窗==============
  const alertTitle = ref('提示')
  const alertShow = ref(false)
  const alertConfirmText = ref('确认')
  // const alertCancelText = ref('取消')
  const alertMessage = ref('变更后，已经填写的资料信息将被清空，确定要更改吗？')

  const alertConfirm = () => {
    alertShow.value = false
    // 存储新的路由信息
    store.commit('ClaimShopStore/UPDATE_LAST_POI_SHOP_INFO')
    // 表单只留poi信息
    store.commit('ClaimShopStore/RESET_CLAIM_STORE_FORM_DATA_KEEP_POI')
    // 清空当前流程的id
    store.commit('ShopApplyStore/UPDATE_SHOP_APPLY_STATE', { id: '' })
    getCanClaimPoiMerchantApi()
  }

  const alertCloseAlert = () => {
    alertShow.value = false
    // 把旧版数据进行更换
    store.commit('ClaimShopStore/UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE', { ...prevRouteParams.value.lastPoiShopInfo })
    routerPushClaimShop()
  }

  // ==============按钮==============
  const loading = ref<boolean>(false)

  const goBack = () => {
    router.back()
  }

  const routerPushClaimShop = () => {
    router.push({
      name: ROUTE_NAME.CLAIM_SHOP,
      query: {
        fullscreen: 'true'
      }
    })
  }

  // 入驻：门店是否支持入驻 + 加灰度逻辑
  const getCanClaimPoiMerchantApi = async () => {
    try {
      loading.value = true

      const params = {
        xhsCategoryId: claimStorePageFormData.xhsCategoryId
      }
      const res = await getCanClaimPoiMerchant(params)
      if (res?.canMerchant) {
        store.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', userInfo.claimType)
      } else {
        store.commit('ShopUserInfoStore/UPDATE_CUSTOM_CLAIM_TYPE', ClaimTypeEnum.LIGHT_CLAIM)
      }

      // 当你更新了poi信息，需要清空表单除去poi信息剩余部分
      store.commit('ClaimShopStore/RESET_CLAIM_STORE_FORM_DATA_KEEP_POI', userInfo.claimType)

      routerPushClaimShop()
    } catch (error) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: typeof error === 'object' && error && 'message' in error
          ? (error as any).message
          : String(error),
      })
      return false
    } finally {
      loading.value = false
    }
  }

  // 选择保存门店
  const next = async () => {
    try {
      const oldPoiId = prevRouteParams.value.lastPoiShopInfo.poiId
      const newPoiId = claimStorePageFormData.poiId

      // 当前存在的poi信息，并且更换成新的poi信息了
      if (oldPoiId && oldPoiId !== newPoiId) {
        alertShow.value = true
      } else {
        getCanClaimPoiMerchantApi()
      }
    } catch (error) {
      showToast({
        type: ToastType.ToastBuiltInType.TEXT,
        message: typeof error === 'object' && error && 'message' in error
          ? (error as any).message
          : String(error),
      })
    }
  }

  // ==============生命周期==============
  const init = () => {
    getInitFormData(claimStorePageFormData)
  }

  onMounted(() => {
    init()
  })

  return {
    // 数据
    formItems,
    FormItemTypeEnum,

    // 变量
    formModel,
    loading,
    alertTitle,
    alertShow,
    alertConfirmText,
    alertMessage,
    alertConfirm,
    alertCloseAlert,

    // 计算属性

    // 方法
    goBack,
    next

  }
}
