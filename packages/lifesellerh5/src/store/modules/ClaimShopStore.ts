// 依赖
import { cloneDeep, omit } from 'lodash'
// 类型
import {
  IStoreState,
  IPoiShopInfo,
  IUnifiedClaimStoreFormData,
  PrevRouteParams,
} from '../type'

// 初始化状态数据
export const initialClaimStorePageFormData: IUnifiedClaimStoreFormData = {
  // 通用字段初始化
  id: undefined,
  poiId: '',
  poiName: '',
  addressDetail: '',
  companyName: '',

  // 本地轻认领字段
  poiTelephones: [],
  refType: '',
  refSubType: '',
  auditStatus: '',
  certificateQual: undefined,
  licenseChgQual: undefined,
  isSubmit: 0,
  auditRemark: '',
  companyLicense: [],
  isUseProCertification: false,

  // 本地认领提交字段
  gaodeId: '',
  longitude: '',
  latitude: '',
  provinceName: '',
  cityName: '',
  districtName: '',

  useProfessionalQualification: false,
  businessLicenseType: undefined,
  businessLicenseImage: undefined,
  businessAddress: '',
  usci: '',
  businessLicenseValidity: undefined,
  qualificationMap: {},
  userId: '',
  sourceFrom: '',
  sourceIdentity: '',
  categoryId: '',
  shopId: '',
  fusionLegaInfo: false,
  poiLegalPersonQual: undefined,
  unchangedSecureFields: [],
  useProCertification: false,

}

// 初始化poi信息
export const initialPoiShopInfo: IPoiShopInfo = {
  poiId: '', // 必填
  poiName: '', // 必填
  addressDetail: '',
  // 以下为选填，不写就是undefined
  provinceName: '',
  cityName: '',
  claimStatus: undefined,
  districtName: '',
  gaodeId: '',
  latitude: '',
  longitude: '',
  noteCount: undefined,
  xhsCategory: '',
  xhsCategoryId: '',
}

// 上一次的路由参数，用于对比
export const initialPrevRouteParams: PrevRouteParams = {
  applyId: null,
  shopId: null,
  lastPoiShopInfo: cloneDeep(initialPoiShopInfo)
}

// store数据
const state: IStoreState = {
  // UI组件库主题
  colorMode: 'platformLight',
  // 认领门店表单数据
  claimStorePageFormData: { ...cloneDeep(initialClaimStorePageFormData), ...cloneDeep(initialPoiShopInfo) },
  // 旧版信息用于对比
  prevRouteParams: { ...cloneDeep(initialPrevRouteParams) }
}

const mutations = {
  // 更新认领门店表单数据
  UPDATE_CLAIM_STORE_PAGE_FORM_DATA_STORE(state: IStoreState, payload: Partial<IUnifiedClaimStoreFormData>) {
    state.claimStorePageFormData = { ...state.claimStorePageFormData, ...payload }
  },
  // 重置认领门店表单数据
  RESET_CLAIM_STORE_PAGE_FORM_DATA_STORE(state: IStoreState) {
    state.claimStorePageFormData = { ...cloneDeep(initialClaimStorePageFormData), ...cloneDeep(initialPoiShopInfo) }
  },
  // 重置表单数据但保留 POI 信息
  RESET_CLAIM_STORE_FORM_DATA_KEEP_POI(state: IStoreState) {
    // POI 字段列表
    const poiFields = Object.keys(initialPoiShopInfo)

    // 提取当前的 POI 信息
    const currentPoiInfo: Partial<IPoiShopInfo> = {}
    poiFields.forEach(field => {
      if (field in state.claimStorePageFormData) {
        (currentPoiInfo as any)[field] = state.claimStorePageFormData[field as keyof typeof state.claimStorePageFormData]
      }
    })

    // 获取初始的非 POI 数据
    const initialNonPoiData = omit(cloneDeep(initialClaimStorePageFormData), poiFields)

    // 合并数据
    state.claimStorePageFormData = {
      ...initialNonPoiData,
      ...cloneDeep(initialPoiShopInfo), // POI 初始值
      ...currentPoiInfo // 当前 POI 值
    } as IUnifiedClaimStoreFormData & IPoiShopInfo
  },

  // ==============存储旧版参数用于对比加回显==============
  // 更新路由参数
  UPDATE_PREV_ROUTE_PARAMS_STORE(state: IStoreState, payload: Partial<PrevRouteParams>) {
    state.prevRouteParams = { ...state.prevRouteParams, ...payload }
  },
  // 重置路由参数
  RESET_PREV_ROUTE_PARAMS_STORE(state: IStoreState) {
    state.prevRouteParams = { ...cloneDeep(initialPrevRouteParams) }
  },
  // 保存当前POI信息到lastPoiShopInfo
  UPDATE_LAST_POI_SHOP_INFO(state: IStoreState) {
    // 提取当前表单中的POI字段
    const poiFields = Object.keys(initialPoiShopInfo) as (keyof IPoiShopInfo)[]
    const currentPoiInfo: Partial<IPoiShopInfo> = {}

    poiFields.forEach(field => {
      if (field in state.claimStorePageFormData) {
        (currentPoiInfo as any)[field] = state.claimStorePageFormData[field as keyof typeof state.claimStorePageFormData]
      }
    })

    // 存储到prevRouteParams.lastPoiShopInfo
    state.prevRouteParams.lastPoiShopInfo = cloneDeep(currentPoiInfo as IPoiShopInfo)
  },

}

const actions = {}

const getters = {
  // 获取门店信息
  poiShopInfoGetters: state => state.poiShopInfo,
}

export default {
  state,
  mutations,
  actions,
  getters,
  namespaced: true,
}
