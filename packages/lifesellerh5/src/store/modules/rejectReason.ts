// 依赖
import { cloneDeep } from 'lodash'
import { IData as IDepositDetailData, getDepositDetail, IGetDepositDetailPayload } from '~/services/edith_get_deposit_detail'

const list = [
  {
      "fieldCode": "merchantCompanyDraft.companyName",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "主体公司名称",
      "moduleCode": "LICENSE"
  },
  {
      "fieldCode": "merchantCompanyDraft.companyRegisteredAddress",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "主体公司注册地址",
      "moduleCode": "LICENSE"
  },
  {
      "fieldCode": "merchantCompanyDraft.uniformCreditCode",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "主体公司统一社会信用代码",
      "moduleCode": "LICENSE"
  },
  {
      "moduleCode": "LICENSE",
      "fieldCode": "merchantCompanyDraft.businessLicense.fileAttachmentList",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "主体公司营业执照（注册证件）图片"
  },
  {
      "fieldName": "主体公司营业执照（注册证件）",
      "moduleCode": "LICENSE",
      "fieldCode": "merchantCompanyDraft.businessLicense.qualificationName",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！"
  },
  {
      "fieldCode": "merchantAuthorizationDraft.certificateType",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "法人证件类型",
      "moduleCode": "LEGAL_PERSON"
  },
  {
      "fieldCode": "merchantAuthorizationDraft.faceIdentification",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "",
      "moduleCode": "LEGAL_PERSON"
  },
  {
      "fieldCode": "merchantAuthorizationDraft.certificateNumber",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "法人证件号码",
      "moduleCode": "LEGAL_PERSON"
  },
  {
      "fieldCode": "merchantAuthorizationDraft.authorizationName",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "法人姓名",
      "moduleCode": "LEGAL_PERSON"
  },
  {
      "moduleCode": "LEGAL_PERSON",
      "fieldCode": "merchantAuthorizationDraft.qualificationList*889821130321592320.fileAttachmentList",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "法人资质图片"
  },
  {
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "法人资质图片",
      "moduleCode": "LEGAL_PERSON",
      "fieldCode": "merchantAuthorizationDraft.qualificationList*889821308189442048.fileAttachmentList"
  },
  {
      "fieldName": "门店认领",
      "moduleCode": "POI_CLAIM",
      "fieldCode": "poiInfo.poiId",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！"
  },
  {
      "fieldCode": "poiInfo.poiName",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "门店认领",
      "moduleCode": "POI_CLAIM"
  },
  {
      "fieldCode": "poiInfo.poiAddress",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "门店认领",
      "moduleCode": "POI_CLAIM"
  },
  {
      "fieldCode": "poiInfo.poiCategory",
      "rejectContent": "您好，您填写的店铺名称不符合平台命名规范，店铺名称中不得含有误导他人、仿冒他人、不文明、营销引流及其他不适宜作为店铺名称的信息，建议修改为您的企业商号或与所售商品相关的店铺名称，谢谢！",
      "fieldName": "门店认领",
      "moduleCode": "POI_CLAIM"
  }
]

// 初始化驳回详情数据
const initRejectReasonData = {
  depositDetail: null as IDepositDetailData | null,
  rejectDetailList: list as any[],
  businessLicenseOriginalFormData: {} as any, // 存储营业执照原始表单数据
  legalPersonOriginalFormData: {} as any, // 存储法人原始表单数据
  QualificationOriginalFormData: {} as any, // 存储资质原始表单数据
  loading: false,
  error: null as string | null
}

// state
const state = {
  rejectReasonState: cloneDeep(initRejectReasonData)
}

// mutations
const mutations = {
  // 设置驳回详情数据
  SET_DEPOSIT_DETAIL(state: any, payload: IDepositDetailData | null) {
    state.rejectReasonState.depositDetail = payload
    state.rejectReasonState.rejectDetailList = payload?.rejectDetailList || []
  },

  SET_DEPOSIT_DETAIL_LIST(state: any, payload: any[]) {
    state.rejectReasonState.rejectDetailList = payload
  },

  // 设置营业执照原始数据
  SET_BUSINESS_LICENSE_ORIGINAL_DATA(state: any, payload: any) {
    state.rejectReasonState.businessLicenseOriginalFormData = payload
  },

  // 设置法人原始数据
  SET_LEGAL_PERSON_ORIGINAL_DATA(state: any, payload: any) {
    state.rejectReasonState.legalPersonOriginalFormData = payload
  },

  // 设置资质原始数据
  SET_QUALIFICATION_ORIGINAL_DATA(state: any, payload: any) {
    state.rejectReasonState.QualificationOriginalFormData = payload
  },

  // 设置加载状态
  SET_LOADING(state: any, payload: boolean) {
    state.rejectReasonState.loading = payload
  },

  // 设置错误信息
  SET_ERROR(state: any, payload: string | null) {
    state.rejectReasonState.error = payload
  },

  // 清空数据
  CLEAR_DATA(state: any) {
    state.rejectReasonState = cloneDeep(initRejectReasonData)
  }
}

// actions
const actions = {
  // 获取驳回原因列表
  async getRejectReasonList({ commit }: any, { applyId, moduleCode }: { applyId: string; moduleCode?: string }) {
    if (!applyId) {
      console.warn('获取驳回原因列表失败：缺少applyId')
      return
    }

    try {
      commit('SET_LOADING', true)
      commit('SET_ERROR', null)

      // 根据接口定义，moduleCode是可选的
      const params: IGetDepositDetailPayload = {
        moduleCode: moduleCode || 'merchantCompanyDraft' // 默认使用营业执照模块
      }
      const response = await getDepositDetail(params)
      commit('SET_DEPOSIT_DETAIL', response)

      console.log('获取到的驳回原因列表:', response?.rejectDetailList)
    } catch (error: any) {
      console.error('获取驳回原因列表失败:', error)
      commit('SET_ERROR', error.message || '获取驳回原因列表失败')
      commit('SET_DEPOSIT_DETAIL', null)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 清空驳回原因数据
  clearRejectReasonData({ commit }: any) {
    commit('CLEAR_DATA')
  },

  // 设置营业执照原始数据
  setBusinessLicenseOriginalData({ commit }: any, data: any) {
    commit('SET_BUSINESS_LICENSE_ORIGINAL_DATA', data)
  },

  // 设置法人原始数据
  setLegalPersonOriginalData({ commit }: any, data: any) {
    commit('SET_LEGAL_PERSON_ORIGINAL_DATA', data)
  },

  // 设置资质原始数据
  setQualificationOriginalData({ commit }: any, data: any) {
    commit('SET_QUALIFICATION_ORIGINAL_DATA', data)
  }
}

// getters
const getters = {
  // 获取驳回详情数据
  depositDetail: (state: any) => state.rejectReasonState.depositDetail,

  // 获取驳回详情列表
  rejectDetailList: (state: any) => state.rejectReasonState.rejectDetailList,

  // 获取原始表单数据
  businessLicenseOriginalFormData: (state: any) => state.rejectReasonState.businessLicenseOriginalFormData,
  legalPersonOriginalFormData: (state: any) => state.rejectReasonState.legalPersonOriginalFormData,
  qualificationOriginalFormData: (state: any) => state.rejectReasonState.QualificationOriginalFormData,

  // 获取加载状态
  loading: (state: any) => state.rejectReasonState.loading,

  // 获取错误信息
  error: (state: any) => state.rejectReasonState.error
}

export default {
  state,
  mutations,
  actions,
  getters,
  namespaced: true
}
