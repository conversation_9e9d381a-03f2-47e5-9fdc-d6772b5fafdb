// ==========ClaimShopStore类型==============
// 导入A类型相关接口
import type {
  IBusinessLicenseImage,
  IBusinessLicenseValidity,
  IPoiLegalPersonQual,
  ICertificateQual,
  ILicenseChgQual
} from '~/services/shopServicesTypes'

// 导入统一的资质类型定义
import type { QualificationMap } from '~/types/qualification'

// 门店信息类型定义
export type IPoiShopInfo = {
  /** 门店ID，必填 */
  poiId: string
  /** 门店名称，必填 */
  poiName: string
  /** 详细地址，必填 */
  addressDetail: string

  /** 省份名称，选填 */
  provinceName?: string
  /** 城市名，选填 */
  cityName?: string
  /** 认领状态，选填 */
  claimStatus?: number
  /** 区（县）名称，选填 */
  districtName?: string
  /** 高德POI ID，选填 */
  gaodeId?: string
  /** 纬度，选填 */
  latitude?: string
  /** 经度，选填 */
  longitude?: string
  /** 备注数量，选填 */
  noteCount?: number
  /** 分类 */
  xhsCategory?: string
  /** 分类ID */
  xhsCategoryId?: string
}

// 统一的表单数据接口
export type IUnifiedClaimStoreFormData = {
  // ==================== 通用字段 ====================
  /** 记录ID */
  id?: number
  /** 门店ID（必填） */
  poiId: string
  /** 门店名称 */
  poiName?: string
  /** 公司名称 */
  companyName?: string
  /** 详细地址 */
  addressDetail?: string

  // ==================== 轻认领字段 ====================
  /** 门店联系电话列表 */
  poiTelephones?: string[]
  /** 主体性质（必填） */
  refType?: string
  /** 主体类型 */
  refSubType?: string
  /** 审核状态（通过枚举转换后的状态） */
  auditStatus?: string
  /** 其它证明性材料 */
  certificateQual?: ICertificateQual
  /** 营业执照变更证明 */
  licenseChgQual?: ILicenseChgQual
  /** 是否提交 (0: 未提交, 1: 已提交) */
  isSubmit?: number
  /** 审核拒绝原因/审核备注 */
  auditRemark?: string
  /** 营业执照图片列表（图片URL数组） */
  companyLicense?: string[]
  /** 是否使用专业认证 */
  isUseProCertification?: boolean

  // ==================== 本地认领表单字段 ====================
  /** 高德ID */
  gaodeId?: string
  /** 经度 */
  longitude?: string
  /** 纬度 */
  latitude?: string
  /** 省份名称 */
  provinceName?: string
  /** 城市名称 */
  cityName?: string
  /** 区/县名称 */
  districtName?: string
  /** 营业执照类型 {"INDIVIDUAL_BUSINESS":0个体,"ORDINARY_BUSINESS":1普企,"OTHER":2} */
  businessAddress?: string
  businessLicenseType?: number
  /** 营业执照图片信息（媒体信息 图片、视频） */
  businessLicenseImage?: IBusinessLicenseImage
  /** 统一社会信用代码 */
  usci?: string
  /** 营业执照有效期信息 */
  businessLicenseValidity?: IBusinessLicenseValidity
  /** 是否使用专业号资质 */
  useProfessionalQualification?: boolean

  /** 行业资质Map（资质code -> 资质信息） */
  qualificationMap?: QualificationMap
  /** 用户ID */
  userId?: string
  /** 来源 */
  sourceFrom?: string
  /** 来源身份标识 */
  sourceIdentity?: string
  /** 类目ID */
  categoryId?: string
  /** 门店ID（业务相关） */
  shopId?: string
  /** 是否合并提交法人信息 */
  fusionLegaInfo?: boolean
  /** 法人资质信息 */
  poiLegalPersonQual?: IPoiLegalPersonQual
  /** 未加密字段列表 */
  unchangedSecureFields?: string[]
  /** 使用专业号资质 */
  useProCertification?: boolean
}

// 上一次的路由参数，用于对比
export type PrevRouteParams = {
  applyId?: string | null
  shopId?: string | null
  lastPoiShopInfo?: IPoiShopInfo
}

// Store State接口
export interface IStoreState {
  colorMode: 'platformLight' | 'platformDark' | string
  claimStorePageFormData: IUnifiedClaimStoreFormData & IPoiShopInfo
  prevRouteParams: PrevRouteParams
}
